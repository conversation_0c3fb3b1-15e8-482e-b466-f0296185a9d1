{"openapi": "3.0.1", "info": {"title": "CancerMRAllShow", "version": "v1"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginReqDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginReqDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginReqDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/test": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "a", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/testauth": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/AuthCheck": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/AesEncrypt": {"get": {"tags": ["Cryptography"], "parameters": [{"name": "cipherText", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/AesDecrypt": {"get": {"tags": ["Cryptography"], "parameters": [{"name": "cipherText", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Gen/GetCancerMapping": {"get": {"tags": ["Gen"], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/GetCancerMRList": {"post": {"tags": ["MRData"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancerBasicCondition"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CancerBasicCondition"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CancerBasicCondition"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MRData/GetMRBasicByMRNoOrName": {"get": {"tags": ["MRData"], "parameters": [{"name": "mrno", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "name", "in": "query", "schema": {"type": "string", "default": ""}}, {"name": "mrNoOrName", "in": "query", "schema": {"type": "string", "default": ""}}], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/GetOEARecs": {"get": {"tags": ["MRData"], "parameters": [{"name": "SDate", "in": "query", "schema": {"type": "string"}}, {"name": "EDate", "in": "query", "schema": {"type": "string"}}, {"name": "MRNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/GetOpdSummary": {"get": {"tags": ["MRData"], "parameters": [{"name": "seeDate", "in": "query", "schema": {"type": "string"}}, {"name": "pnc", "in": "query", "schema": {"type": "string"}}, {"name": "room", "in": "query", "schema": {"type": "string"}}, {"name": "regNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/GetDischargeNote": {"get": {"tags": ["MRData"], "parameters": [{"name": "SDate", "in": "query", "schema": {"type": "string"}}, {"name": "EDate", "in": "query", "schema": {"type": "string"}}, {"name": "MRNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/SoapData": {"get": {"tags": ["MRData"], "parameters": [{"name": "SDate", "in": "query", "schema": {"type": "string"}}, {"name": "EDate", "in": "query", "schema": {"type": "string"}}, {"name": "MRNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/GetAdmDrg": {"get": {"tags": ["MRData"], "parameters": [{"name": "SDate", "in": "query", "schema": {"type": "string"}}, {"name": "EDate", "in": "query", "schema": {"type": "string"}}, {"name": "MRNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/GetOpdDrg": {"get": {"tags": ["MRData"], "parameters": [{"name": "SDate", "in": "query", "schema": {"type": "string"}}, {"name": "EDate", "in": "query", "schema": {"type": "string"}}, {"name": "MRNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/LabData": {"get": {"tags": ["MRData"], "parameters": [{"name": "SDate", "in": "query", "schema": {"type": "string"}}, {"name": "EDate", "in": "query", "schema": {"type": "string"}}, {"name": "MRNo", "in": "query", "schema": {"type": "string"}}, {"name": "byVerDate", "in": "query", "schema": {"type": "string", "default": "0"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "text/json": {"schema": {"type": "array", "items": {"type": "string"}}}, "application/*+json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/MRData/LabReport": {"get": {"tags": ["MRData"], "parameters": [{"name": "SDate", "in": "query", "schema": {"type": "string"}}, {"name": "EDate", "in": "query", "schema": {"type": "string"}}, {"name": "MRNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/PathologyReport": {"get": {"tags": ["MRData"], "parameters": [{"name": "SDate", "in": "query", "schema": {"type": "string"}}, {"name": "EDate", "in": "query", "schema": {"type": "string"}}, {"name": "MRNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/SpecialExamReport": {"get": {"tags": ["MRData"], "parameters": [{"name": "SDate", "in": "query", "schema": {"type": "string"}}, {"name": "EDate", "in": "query", "schema": {"type": "string"}}, {"name": "MRNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/NucReport": {"get": {"tags": ["MRData"], "parameters": [{"name": "SDate", "in": "query", "schema": {"type": "string"}}, {"name": "EDate", "in": "query", "schema": {"type": "string"}}, {"name": "MRNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/RadReport": {"get": {"tags": ["MRData"], "parameters": [{"name": "SDate", "in": "query", "schema": {"type": "string"}}, {"name": "EDate", "in": "query", "schema": {"type": "string"}}, {"name": "MRNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/MRData/Operation": {"get": {"tags": ["MRData"], "parameters": [{"name": "SDate", "in": "query", "schema": {"type": "string"}}, {"name": "EDate", "in": "query", "schema": {"type": "string"}}, {"name": "MRNo", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Test/GetOpdSummary": {"get": {"tags": ["Test"], "parameters": [{"name": "seeDate", "in": "query", "schema": {"type": "string"}}, {"name": "pnc", "in": "query", "schema": {"type": "string"}}, {"name": "room", "in": "query", "schema": {"type": "string"}}, {"name": "regNo", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"CancerBasicCondition": {"type": "object", "properties": {"mrNoOrName": {"type": "string", "nullable": true}, "cancerEName": {"type": "array", "items": {"type": "string"}, "nullable": true}, "mrNo": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginReqDTO": {"type": "object", "properties": {"UserID": {"type": "string", "nullable": true}, "Password": {"type": "string", "nullable": true}, "ExpDays": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"Date": {"type": "string", "format": "date"}, "TemperatureC": {"type": "integer", "format": "int32"}, "TemperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "Summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}