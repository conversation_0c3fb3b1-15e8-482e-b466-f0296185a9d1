import React from 'react';
import { ProgressSpinner } from 'primereact/progressspinner';

// Spinner 說明
/*
    環境
    0.安裝primeReact
        npm install primereact --save
        npm install primeicons --save
    1.引入Spinner模組
        import Spinner from '../../components/spinner';
    2.宣告    
        const [loading, setLoading] = useState(false)
    3.使用方式
        {loading && <Spinner overlay={true} />}
        
        setLoading(true);
        setLoading(false);
*/


export interface SpinnerProps {
    overlay?: boolean;
}

export default function Spinner({ overlay }: SpinnerProps = { overlay: false }) {
    if (overlay) {
        return (
            <div>
                <div style={overlayStyle} className=' z-50'>
                    <div style={containerStyle}>
                        <ProgressSpinner style={{ width: '50px', height: '50px' }} strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" />
                    </div>
                </div>
            </div>
        );
    } else {
        return (
            <div className="card">
                <ProgressSpinner style={{ width: '50px', height: '50px' }} strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" />
            </div>
        );
    }
}
const overlayStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
};

const containerStyle: React.CSSProperties = {
    backgroundColor: 'white',
    padding: '20px',
    borderRadius: '8px',
    boxShadow: '0 0 10px rgba(0, 0, 0, 0.2)'
};