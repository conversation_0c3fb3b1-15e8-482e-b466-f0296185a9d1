'use client';
import { InputText } from 'primereact/inputtext';
import React, { FC, use, useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { InputTextarea } from 'primereact/inputtextarea';
import { MRDataService, OEARecsIF, OpdSummaryIF, SoapDataIF, LabDataIF } from '@/services/MRDataService';
import { Button } from 'primereact/button';
import { FloatLabel } from "primereact/floatlabel";
import { Timeline } from 'primereact/timeline';
import { Card } from 'primereact/card';
import { SelectButton } from 'primereact/selectbutton';
import { convertToTWDateFromDateString, convertToTWDateHyphenFromDate, convertToTWDateHyphenFromTWDateString } from '@/utils/DataFormatter';
import { setToastRef, showError, showInfo } from '@/components/Toast';
import { Toast } from 'primereact/toast';
import { MRBasicIF } from '@/models/MRData';
import { Calendar } from 'primereact/calendar';
import { addLocale, locale } from 'primereact/api';
import { Dropdown } from 'primereact/dropdown';
import { Dialog } from 'primereact/dialog';
import CaseOpdSoap from '@/components/CaseDetail/CaseOpdSoap';
import { Accordion, AccordionTab } from 'primereact/accordion';
import Cookies from 'universal-cookie';
import BlankFrame from '@/components/Tab/BlankFrame';
import LabData from '@/components/LabData';
import OEATimeLine from '@/components/CaseDetail/OEATimeLine';
import { ProgressSpinner } from 'primereact/progressspinner';

interface CaseDetailProps {
    mrNo: string;
    patientName: string;
}
// TimeLine Event
interface TimelineEventIF {
    opdType?: string;
    title?: string;
    subTitle?: string;
    content?: string;
    icon?: string;
    color?: string;
    image?: string;
    tmId?: string;
    hospType?: string;
}
// 門急診4Key IF 日期時段診間序號
interface opdDetail {
    seeDate: string;
    pnc: string;
    room: string;
    seqNo: number;
}


const CaseDetail: FC<CaseDetailProps> = ({ mrNo, patientName }) => {
    const [value, setValue] = useState<string>('TEST');
    const [text, setText] = useState<string>('');
    const _mrDataService = useMemo(() => new MRDataService(), []);
    const [isViewOnly, setIsViewOnly] = useState<boolean>(true);
    const [mrBasic, setMRBasic] = useState<MRBasicIF | null>(null);
    const [OEARecs, setOEARecs] = useState<OEARecsIF | null>(null);
    const [soapData, setSoapData] = useState<SoapDataIF[] | null>(null);
    const [opdSummaryVisible, setOpdSummaryVisible] = useState<boolean>(false);
    const [opdDetail, setOpdDetail] = useState<opdDetail | null>(null);
    const [teamMeetingVisible, setTeamMeetingVisible] = useState<boolean>(false);
    const [sDate, setSDate] = useState<string>('');
    const [eDate, setEDate] = useState<string>('');

    // 時間線事件
    const [timelineEvents, setTimelineEvents] = useState<TimelineEventIF[]>([]);

    const timelineRef = useRef<HTMLDivElement>(null);
    const timelineScrollPositionRef = useRef(0);

    // 從Cookie中抓 USER_BASIC 資料
    const cookie = new Cookies();
    const UserBasic: UserBasicIF = cookie.get('USER_BASIC');

    // 初始化日期範圍
    const [initialized, setInitialized] = useState(false);

    // getTeamMeetingUrl
    const getTeamMeetingUrl = (mrNo: string) => {
        return 'http://hlcandbsvr/CancerPlan/TeamMeeting?MRNo=' + mrNo + '&CancerName=ALL&Edit=N&DocNo=&UserID=' + UserBasic.userID;
    }

    // GetMRBasicByMRNoOrName
    const GetMRBasic = useCallback(async (mrno: string) => {
        try {
            const data = await _mrDataService.GetMRBasicByMRNoOrName(mrno);
            const result = data[0] ?? null;
            setMRBasic(result);
            console.log('MRBasic', result);
        } catch (error) {
            console.log(error);
        }
    }, [_mrDataService]);

    // 抓門急住資料 GetOEARecs
    const GetOEARecs = useCallback(async (sDate: string, eDate: string, mrno: string) => {
        try {
            if (!mrno) return;
            // 如果sDate 沒有值就傳入今天的5年前 民國年月日
            if (!sDate) {
                const today = new Date();
                const fiveYearsAgo = new Date(today.getFullYear() - 5, today.getMonth(), today.getDate());
                sDate = convertToTWDateFromDateString(fiveYearsAgo.toISOString());
            }
            // 如果 eDate 沒有值就傳入今天的 民國年月日
            if (!eDate) {
                const today = new Date();
                eDate = convertToTWDateFromDateString(today.toISOString());
            }
            const data = await _mrDataService.GetOEARecs({ mrNo: mrno, sDate: sDate, eDate: eDate });
            console.log('OEARecs', data);
            setOEARecs(data);
            return data;
        } catch (error) {
            console.log(error);
        }
    }, [_mrDataService]);

    // 抓soapData
    const GetSoapData = useCallback(async (sDate: string, eDate: string, mrNo: string) => {
        try {
            const result = await _mrDataService.SoapData({ mrNo, sDate: sDate, eDate: eDate });
            console.log('SoapData', result);
            setSoapData(result);
            return result;
        } catch (error) {
            console.log(error);
        }
    }, [_mrDataService]);

    // 將OEARecs 中的 門急診資料和住院資料轉換成時間線事件
    const ConvertOEARecsToTimeline = useCallback((sourse: any): TimelineEventIF[] => {
        const events: TimelineEventIF[] = [];
        const _OEARecs: OEARecsIF = sourse.OEARecs;
        const _soapDataList: SoapDataIF[] | undefined = sourse.SoapData
        if (!_OEARecs) return events;

        // 處理門急住資料
        _OEARecs.OERecs.forEach((oeRec) => {
            // 組合content 從soapData 依照 日期、科別名稱、醫師名稱 找到對應的資料 抓診斷
            let contentData = '';
            if (_soapDataList) {
                const soap: SoapDataIF | undefined = _soapDataList.find((soap) => {
                    return soap.SeeDate === oeRec.chDate &&
                        soap.SecName === oeRec.chSecName &&
                        soap.DocName === oeRec.chDocName;
                });
                if (soap) {
                    contentData += soap.Icd10No1 + ' ' + soap.Icd10Name1 + '\n';
                    contentData += soap.Icd10No2 + ' ' + soap.Icd10Name2 + '\n';
                    contentData += soap.Icd10No3 + ' ' + soap.Icd10Name3 + '\n';
                }
            }
            let opdEvent: TimelineEventIF = new Object();
            opdEvent.title = convertToTWDateHyphenFromTWDateString(oeRec.chDate) + ' ' + oeRec.chSecName;
            if (oeRec.chRoom === 'AAAA') {
                opdEvent.opdType = '強批';
            }
            if (oeRec.chRoom === 'LLLL') {
                opdEvent.opdType = '連處';
            }
            if (oeRec.chRoom === 'AAAA' || oeRec.chRoom === 'LLLL') {
                opdEvent.subTitle = oeRec.chSecName + ' ' + oeRec.chDocName + ' 醫師';
            } else {
                opdEvent.subTitle = '門急診 ' + oeRec.chDocName + ' 醫師';
            }
            opdEvent.content = contentData;
            opdEvent.icon = 'pi pi-users';
            opdEvent.color = '#FF9800';
            //日期時段診間序號
            opdEvent.tmId = 'OPD' + '_' + oeRec.chDate + '_' + oeRec.chPNC + '_' + oeRec.chRoom + '_' + oeRec.intRegNo;
            events.push(opdEvent);
        });
        // 處理住院資料 
        _OEARecs.AdmRecs.forEach((admRec) => {
            events.push({
                title: convertToTWDateHyphenFromTWDateString(admRec.chAd1Date) + ' ' + admRec.chSecName,
                subTitle: '住院 ' + admRec.chDocName + ' 醫師',
                icon: 'pi pi-home',
                color: '#ff3b3b',
                tmId: 'ADM' + '_' + mrNo + '_' + admRec.chAd1Date + '_' + admRec.chAd1DateC
            });
        });
        // 依照title升冪排序
        events.sort((a, b) => {
            if (a.title && b.title) {
                return a.title.localeCompare(b.title);
            }
            return 0;
        });
        return events;
    }, [mrNo]);

    // 渲染 門急住資料 時間線
    const RenderOEARecsTimeline = useCallback(async (_mrno: string, sDate: string, eDate: string) => {
        try {
            var _OEARecs = await GetOEARecs(sDate, eDate, _mrno);
            var _SoapData = await GetSoapData(sDate, eDate, _mrno);
            var eventData = ConvertOEARecsToTimeline({ OEARecs: _OEARecs, SoapData: _SoapData });
            setTimelineEvents(eventData);
        } catch (error) {
            showError(error);
        }
    }, [GetOEARecs, GetSoapData, ConvertOEARecsToTimeline]);

    // 將滾輪事件處理抽出成單獨的函數
    const setupWheelEvent = useCallback(() => {
        const timeline = document.getElementById('mrTimeline');
        if (timeline) {
            const handleWheel = (event: WheelEvent) => {
                event.preventDefault();
                const scrollWidth = 900;
                clearTimeout((timeline as any).timer);
                (timeline as any).timer = setTimeout(() => {
                    timeline.scrollTo({
                        left: event.deltaY > 0 ?
                            timeline.scrollLeft + scrollWidth :
                            timeline.scrollLeft - scrollWidth,
                        behavior: 'smooth'
                    });
                }, 0);
            };

            timeline.addEventListener('wheel', handleWheel);
            return () => timeline.removeEventListener('wheel', handleWheel);
        }
        return undefined;
    }, []);

    // 抓檢驗資料  LabDataIF
    const GetLabData = useCallback(async (sDate: string, eDate: string, mrno: string): Promise<LabDataIF[] | undefined> => {
        try {
            const data = await _mrDataService.GetLabData(sDate, eDate, mrno);
            // console.log('LabData', data);
            return data;
        } catch (error) {
            console.log(error);
            return undefined;
        }
    }, [_mrDataService]);

    // 初始化數據的 useEffect - 只執行一次
    useEffect(() => {
        if (!initialized && mrNo) {
            setInitialized(true);
            const today = new Date();
            const fiveYearsAgo = new Date(today.getFullYear() - 5, today.getMonth(), today.getDate());
            const sDate = convertToTWDateFromDateString(fiveYearsAgo.toISOString());
            const eDate = convertToTWDateFromDateString(today.toISOString());

            // 設置日期狀態
            setSDate(sDate);
            setEDate(eDate);

            // 並行調用 API
            Promise.all([
                GetMRBasic(mrNo),
                RenderOEARecsTimeline(mrNo, sDate, eDate)
            ]).catch(error => {
                console.error('初始化數據失敗:', error);
            });
        }
    }, [mrNo, initialized, GetMRBasic, RenderOEARecsTimeline]);

    // 滾輪事件綁定的 useEffect
    useEffect(() => {
        if (timelineEvents.length > 0) {
            const timer = setTimeout(() => {
                const cleanup = setupWheelEvent();
                return () => {
                    cleanup?.();
                    clearTimeout(timer);
                };
            }, 100);
        }
    }, [timelineEvents, opdSummaryVisible, teamMeetingVisible, setupWheelEvent]);

    // 各別更新mrBasic屬性
    const handleMRBasicChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target;
        setMRBasic((prevPatientInfo) => ({
            ...prevPatientInfo,
            [name]: value,
        }));
    };
    // 性別選項
    interface sexIF {
        sexValue: string;
        sexContent: string;
    }
    const sexOptions = ['男', '女', '其他'];
    async function Test() {
        // GetOpdSummary  1131009	2	3013	33
        // const result: OpdSummaryIF = await GetOpdSummary('1131009', '2', '3013', '33');
        // alert(JSON.stringify(result));
        setOpdSummaryVisible(true);
    }

    const OEAMarker = (item: TimelineEventIF) => {
        return (
            <span className="flex w-2rem h-2rem align-items-center justify-content-center text-white border-circle z-1 shadow-1" style={{ backgroundColor: item.color }}>
                <i className={item.icon}></i>
            </span>
        );
    };
    const OEAContent_Title = (tmItem: TimelineEventIF) => {
        return (
            <>
                {tmItem.opdType && <span className='text-lg text-green-500'>{tmItem.opdType}<br /></span>}
                <span className='text-2xl'>{tmItem.title}</span>
            </>
        )
    }
    const OEAContent = (tmItem: TimelineEventIF) => {
        const renderTitle = OEAContent_Title(tmItem);
        return (
            <Card title={renderTitle} subTitle={tmItem?.subTitle} className='h-[30vh] mx-3 min-w-80'>
                {tmItem && tmItem.tmId && tmItem.tmId.toUpperCase().startsWith('OPD_') &&
                    <Button label="更多" className="m-4"
                        onClick={() => {
                            if (tmItem.tmId) {
                                if (tmItem.tmId.toUpperCase().startsWith('OPD_')) {
                                    const timeline = document.getElementById('mrTimeline');
                                    if (timeline) {
                                        timelineScrollPositionRef.current = timeline.scrollLeft;
                                    }

                                    const [type, seeDate, pnc, room, seqNo] = tmItem.tmId.split('_');
                                    setOpdDetail({ seeDate: seeDate, pnc: pnc, room: room, seqNo: parseInt(seqNo) });
                                    setOpdSummaryVisible(true);
                                }
                            }
                        }}
                    />
                }
                <p className="break-words">
                    {tmItem.content && tmItem.content.split('\n').map((line, index) => (
                        <React.Fragment key={index}>
                            {line}
                            <br />
                        </React.Fragment>
                    ))}
                </p>
            </Card>
        );
    };

    const MRBasicInfo = () => {
        return (
            <div className='flex-items my-2' >
                <span className={` ${spanStyle}   `}>
                    <label className='font-bold'>病歷號：</label>
                    <label className='font-bold'>{mrBasic?.MRNo}</label>
                </span>
                <span className={` ${spanStyle}   `}>
                    <label className='font-bold'>姓名：</label>
                    <label className='font-bold'>{mrBasic?.Name}</label>
                </span>
                <span className={` ${spanStyle}  ${spanFlex} `} >
                    <label className='font-bold'>性別：</label>
                    <label className='font-bold'>{mrBasic?.Sex}</label>

                </span>
                <span className={` ${spanStyle}   `}>
                    <label className='font-bold'>生日：</label>
                    {mrBasic?.Birthday &&
                        <label className='font-bold'>{convertToTWDateHyphenFromDate(new Date(mrBasic.Birthday))}</label>
                    }
                </span>
                <span className={` ${spanStyle}   `}>
                    <label className='font-bold'>血型：</label>
                    <label className='font-bold'>{mrBasic?.Blood ? mrBasic.Blood : '無'}</label>
                </span>
                <span className={` ${spanStyle} ${spanFlex} `} >
                    <label className='font-bold pr-3'>連絡電話：</label>
                    <div className='flex flex-col'>
                        <label className='font-bold'>{mrBasic?.TelH}</label>
                        <label className='font-bold'>{mrBasic?.TelO}</label>
                    </div>
                </span>
                <span className={` ${spanStyle} ${spanFlex} `} >
                    <label className='font-bold pr-3'>地址：</label>
                    <div className='flex flex-col'>
                        <label className='font-bold'>{mrBasic?.Add1}</label>
                        <label className='font-bold'>{mrBasic?.Add2}</label>
                    </div>
                </span>

            </div>
        )
    }

    const TeamMeeting = () => {
        return (
            <div>
                {teamMeetingVisible &&
                    <Dialog
                        id='teamMeeting'
                        header={"團隊會議-" + mrBasic?.Name}
                        visible={teamMeetingVisible}
                        onShow={() => {
                            // Dialog 開啟後重新綁定事件
                            setTimeout(setupWheelEvent, 0);
                        }}
                        onHide={() => {
                            setTeamMeetingVisible(false);
                            setTimeout(() => {
                                // Dialog 關閉後重新綁定事件
                                setupWheelEvent();
                            }, 0);
                        }}
                        className='w-[99%] h-full '
                        dismissableMask={true}
                        contentClassName='overflow-hidden p-0'
                    >
                        <BlankFrame url={getTeamMeetingUrl(mrNo)} />
                    </Dialog>
                }
            </div>
        )
    }
    const PatientLabData = () => {
        const [lab, setLab] = useState<LabDataIF[]>();

        useEffect(() => {
            // 使用父組件已經計算好的日期範圍，避免重複計算
            if (sDate && eDate && mrNo) {
                const fetchLabData = async () => {
                    const labDataList = await GetLabData(sDate, eDate, mrNo);
                    if (labDataList) {
                        setLab(labDataList);
                    }
                };
                fetchLabData();
            }
        }, [sDate, eDate, mrNo]);

        return (
            <div>
                {!lab && <p><ProgressSpinner />載入中...</p>}
                {lab && <LabData data={lab} />}
            </div>
        );
    };

    const spanStyle = 'text-lg p-4 bg-purple-100 rounded-lg ';
    const spanFlex = 'flex flex-row items-center';
    return (
        <div >
            {opdDetail && (
                <Dialog
                    id='opdSummary'
                    header={`門診摘要-${opdDetail.seeDate}-${mrBasic?.Name}`}
                    visible={opdSummaryVisible}
                    onShow={() => {
                        // Dialog 開啟後重新綁定事件
                        setTimeout(setupWheelEvent, 0);
                    }}
                    onHide={() => {
                        setOpdSummaryVisible(false);
                        setTimeout(() => {
                            const timeline = document.getElementById('mrTimeline');
                            if (timeline) {
                                timeline.scrollLeft = timelineScrollPositionRef.current;
                            }
                            // Dialog 關閉後重新綁定事件
                            setupWheelEvent();
                        }, 0);
                    }}
                    className='w-[90%]'
                    dismissableMask={true}
                >
                    <CaseOpdSoap
                        seeDate={opdDetail.seeDate}
                        pnc={opdDetail.pnc}
                        room={opdDetail.room}
                        seqNo={opdDetail.seqNo}
                    />
                </Dialog>
            )}
            {/* 團隊會議 */}
            <TeamMeeting />


            {/* <TeamMeeting /> */}
            <Toast ref={(ref) => setToastRef(ref)} />
            {/* 病歷資料 */}
            <MRBasicInfo />
            {/* 開啟團隊會議按鈕 */}
            <Button label="開啟團隊會議" onClick={() => setTeamMeetingVisible(true)} severity="success" />


            <Accordion multiple activeIndex={[0, 1]} className='m-2'>
                <AccordionTab header="門急住紀錄">
                    {/* <OEATimeLineOld /> */}
                    <OEATimeLine mrNo={mrNo} />
                </AccordionTab>
                <AccordionTab header="檢驗資料">
                    <PatientLabData />
                </AccordionTab>
            </Accordion>



        </div >
    );
};

export default CaseDetail;