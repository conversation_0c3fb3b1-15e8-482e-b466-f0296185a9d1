'use client';
import React, { FC, useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { MRDataService, LabDataIF } from '@/services/MRDataService';
import { Button } from 'primereact/button';
import { convertToTWDateFromDateString, convertToTWDateHyphenFromDate } from '@/utils/DataFormatter';
import { setToastRef } from '@/components/Toast';
import { Toast } from 'primereact/toast';
import { MRBasicIF } from '@/models/MRData';
import { Dialog } from 'primereact/dialog';
import { Accordion, AccordionTab } from 'primereact/accordion';
import Cookies from 'universal-cookie';
import BlankFrame from '@/components/Tab/BlankFrame';
import LabData from '@/components/LabData';
import OEATimeLine from '@/components/CaseDetail/OEATimeLine';
import { ProgressSpinner } from 'primereact/progressspinner';

interface CaseDetailProps {
    mrNo: string;
    patientName: string;
}
// 移除不再使用的介面，已由 OEATimeLine 組件處理


const CaseDetail: FC<CaseDetailProps> = ({ mrNo }) => {
    const _mrDataService = useMemo(() => new MRDataService(), []);
    const [mrBasic, setMRBasic] = useState<MRBasicIF | null>(null);
    // 移除重複的 opdDetail 狀態，已由 OEATimeLine 組件處理
    const [teamMeetingVisible, setTeamMeetingVisible] = useState<boolean>(false);
    const [sDate, setSDate] = useState<string>('');
    const [eDate, setEDate] = useState<string>('');

    // 移除不再使用的 ref

    // 從Cookie中抓 USER_BASIC 資料
    const cookie = new Cookies();
    const UserBasic: UserBasicIF = cookie.get('USER_BASIC');

    // 初始化日期範圍
    const [initialized, setInitialized] = useState(false);

    // getTeamMeetingUrl
    const getTeamMeetingUrl = (mrNo: string) => {
        return 'http://hlcandbsvr/CancerPlan/TeamMeeting?MRNo=' + mrNo + '&CancerName=ALL&Edit=N&DocNo=&UserID=' + UserBasic.userID;
    }

    // GetMRBasicByMRNoOrName
    const GetMRBasic = useCallback(async (mrno: string) => {
        try {
            const data = await _mrDataService.GetMRBasicByMRNoOrName(mrno);
            const result = data[0] ?? null;
            setMRBasic(result);
            console.log('MRBasic', result);
        } catch (error) {
            console.log(error);
        }
    }, [_mrDataService]);

    // 移除重複的 API 呼叫函數，這些功能已由 OEATimeLine 組件處理

    // 移除重複的時間線渲染邏輯，改由 OEATimeLine 組件處理

    // 將滾輪事件處理抽出成單獨的函數
    const setupWheelEvent = useCallback(() => {
        const timeline = document.getElementById('mrTimeline');
        if (timeline) {
            const handleWheel = (event: WheelEvent) => {
                event.preventDefault();
                const scrollWidth = 900;
                clearTimeout((timeline as any).timer);
                (timeline as any).timer = setTimeout(() => {
                    timeline.scrollTo({
                        left: event.deltaY > 0 ?
                            timeline.scrollLeft + scrollWidth :
                            timeline.scrollLeft - scrollWidth,
                        behavior: 'smooth'
                    });
                }, 0);
            };

            timeline.addEventListener('wheel', handleWheel);
            return () => timeline.removeEventListener('wheel', handleWheel);
        }
        return undefined;
    }, []);

    // 抓檢驗資料  LabDataIF
    const GetLabData = useCallback(async (sDate: string, eDate: string, mrno: string): Promise<LabDataIF[] | undefined> => {
        try {
            const data = await _mrDataService.GetLabData(sDate, eDate, mrno);
            // console.log('LabData', data);
            return data;
        } catch (error) {
            console.log(error);
            return undefined;
        }
    }, [_mrDataService]);

    // 初始化數據的 useEffect - 只執行一次
    useEffect(() => {
        if (!initialized && mrNo) {
            setInitialized(true);
            const today = new Date();
            const fiveYearsAgo = new Date(today.getFullYear() - 5, today.getMonth(), today.getDate());
            const sDate = convertToTWDateFromDateString(fiveYearsAgo.toISOString());
            const eDate = convertToTWDateFromDateString(today.toISOString());

            // 設置日期狀態
            setSDate(sDate);
            setEDate(eDate);

            // 只調用基本資料 API，時間線由 OEATimeLine 組件處理
            GetMRBasic(mrNo).catch(error => {
                console.error('初始化基本資料失敗:', error);
            });
        }
    }, [mrNo, initialized, GetMRBasic]);

    // 滾輪事件綁定的 useEffect
    useEffect(() => {
        const timer = setTimeout(() => {
            const cleanup = setupWheelEvent();
            return () => {
                cleanup?.();
                clearTimeout(timer);
            };
        }, 100);
    }, [teamMeetingVisible, setupWheelEvent]);

    // 移除不再使用的函數

    // 移除重複的時間線渲染組件，這些功能已由 OEATimeLine 組件處理

    const MRBasicInfo = () => {
        return (
            <div className='flex-items my-2' >
                <span className={` ${spanStyle}   `}>
                    <label className='font-bold'>病歷號：</label>
                    <label className='font-bold'>{mrBasic?.MRNo}</label>
                </span>
                <span className={` ${spanStyle}   `}>
                    <label className='font-bold'>姓名：</label>
                    <label className='font-bold'>{mrBasic?.Name}</label>
                </span>
                <span className={` ${spanStyle}  ${spanFlex} `} >
                    <label className='font-bold'>性別：</label>
                    <label className='font-bold'>{mrBasic?.Sex}</label>

                </span>
                <span className={` ${spanStyle}   `}>
                    <label className='font-bold'>生日：</label>
                    {mrBasic?.Birthday &&
                        <label className='font-bold'>{convertToTWDateHyphenFromDate(new Date(mrBasic.Birthday))}</label>
                    }
                </span>
                <span className={` ${spanStyle}   `}>
                    <label className='font-bold'>血型：</label>
                    <label className='font-bold'>{mrBasic?.Blood ? mrBasic.Blood : '無'}</label>
                </span>
                <span className={` ${spanStyle} ${spanFlex} `} >
                    <label className='font-bold pr-3'>連絡電話：</label>
                    <div className='flex flex-col'>
                        <label className='font-bold'>{mrBasic?.TelH}</label>
                        <label className='font-bold'>{mrBasic?.TelO}</label>
                    </div>
                </span>
                <span className={` ${spanStyle} ${spanFlex} `} >
                    <label className='font-bold pr-3'>地址：</label>
                    <div className='flex flex-col'>
                        <label className='font-bold'>{mrBasic?.Add1}</label>
                        <label className='font-bold'>{mrBasic?.Add2}</label>
                    </div>
                </span>

            </div>
        )
    }

    const TeamMeeting = () => {
        return (
            <div>
                {teamMeetingVisible &&
                    <Dialog
                        id='teamMeeting'
                        header={"團隊會議-" + mrBasic?.Name}
                        visible={teamMeetingVisible}
                        onShow={() => {
                            // Dialog 開啟後重新綁定事件
                            setTimeout(setupWheelEvent, 0);
                        }}
                        onHide={() => {
                            setTeamMeetingVisible(false);
                            setTimeout(() => {
                                // Dialog 關閉後重新綁定事件
                                setupWheelEvent();
                            }, 0);
                        }}
                        className='w-[99%] h-full '
                        dismissableMask={true}
                        contentClassName='overflow-hidden p-0'
                    >
                        <BlankFrame url={getTeamMeetingUrl(mrNo)} />
                    </Dialog>
                }
            </div>
        )
    }
    const PatientLabData = () => {
        const [lab, setLab] = useState<LabDataIF[]>();
        const [labLoading, setLabLoading] = useState<boolean>(false);
        const [labLoaded, setLabLoaded] = useState<boolean>(false);

        useEffect(() => {
            // 只在尚未載入且有必要參數時才呼叫 API
            if (sDate && eDate && mrNo && !labLoaded && !labLoading) {
                const fetchLabData = async () => {
                    setLabLoading(true);
                    try {
                        const labDataList = await GetLabData(sDate, eDate, mrNo);
                        if (labDataList) {
                            setLab(labDataList);
                        }
                        setLabLoaded(true);
                    } catch (error) {
                        console.error('載入檢驗資料失敗:', error);
                    } finally {
                        setLabLoading(false);
                    }
                };
                fetchLabData();
            }
        }, [labLoaded, labLoading]); // 移除外部狀態依賴，避免重複呼叫

        return (
            <div>
                {labLoading && <p><ProgressSpinner />載入中...</p>}
                {!labLoading && !lab && labLoaded && <p>無檢驗資料</p>}
                {lab && <LabData data={lab} />}
            </div>
        );
    };

    const spanStyle = 'text-lg p-4 bg-purple-100 rounded-lg ';
    const spanFlex = 'flex flex-row items-center';
    return (
        <div >
            {/* 團隊會議 */}
            <TeamMeeting />


            {/* <TeamMeeting /> */}
            <Toast ref={(ref) => setToastRef(ref)} />
            {/* 病歷資料 */}
            <MRBasicInfo />
            {/* 開啟團隊會議按鈕 */}
            <Button label="開啟團隊會議" onClick={() => setTeamMeetingVisible(true)} severity="success" />


            <Accordion multiple activeIndex={[0, 1]} className='m-2'>
                <AccordionTab header="門急住紀錄">
                    {/* <OEATimeLineOld /> */}
                    <OEATimeLine mrNo={mrNo} />
                </AccordionTab>
                <AccordionTab header="檢驗資料">
                    <PatientLabData />
                </AccordionTab>
            </Accordion>



        </div >
    );
};

export default CaseDetail;