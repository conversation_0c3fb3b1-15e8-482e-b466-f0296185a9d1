
import { Toast } from 'primereact/toast';

// 訊息框模組說明
/*
    環境
    0.安裝primeReact
        npm install primereact --save
        npm install primeicons --save        
    1.請先import Toast模組
        import { setToastRef, showError, showInfo } from '@/components/Toast';
        import { Toast } from 'primereact/toast';
    2.在元件中設定Toast元件
        <Toast ref={(ref) => setToastRef(ref)} />
    3.調用方式
        事件 onClick={() => showInfo('測試Toast')}>

*/
let toast: Toast;

export const setToastRef = (ref: Toast | null) => {
    toast = ref!;
};


// 用enum改寫
export enum MsgType {
    Success = 'success',
    Info = 'info',
    Warn = 'warn',
    Error = 'error',
}

export const showSuccess = (message: any, summary: string = '') => {
    if (toast) {
        showToast(MsgType.Success, summary, message);
    }
};

export const showError = (message: any, summary: string = '') => {
    if (toast) {
        showToast(MsgType.Error, summary, message);
    }
};


export const showInfo = (message: any, summary: string = '') => {
    if (toast) {
        showToast(MsgType.Info, summary, message);
    }
};

export const showWarn = (message: any, summary: string = '') => {
    if (toast) {
        showToast(MsgType.Warn, summary, message);
    }
};



export const showToast = (severity: MsgType, summary: string, detail: any) => {
    // 如果不是字串，轉成JSON字串
    if (typeof detail !== 'string') {
        detail = JSON.stringify(detail);
        console.log('detail', detail);
    }
    toast.show({ severity: severity, summary: summary, detail: detail });
};




