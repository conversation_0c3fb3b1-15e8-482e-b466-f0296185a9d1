# CaseDetail.tsx API 重複呼叫問題修復總結

## 問題分析

### 原始問題
1. **LabData API 重複呼叫**：`PatientLabData` 組件每次依賴變化都會重新呼叫 `GetLabData` API
2. **OEA 資料重複呼叫**：`CaseDetail.tsx` 和 `OEATimeLine.tsx` 都在呼叫相同的 API
3. **缺乏快取機制**：沒有防止重複呼叫的保護機制
4. **不必要的 useEffect 觸發**：依賴管理不當導致重複執行

### 具體問題點
- `RenderOEARecsTimeline` 函數會呼叫 `GetOEARecs` 和 `GetSoapData`
- `OEATimeLine` 組件也會呼叫相同的 API，造成重複
- `PatientLabData` 的 useEffect 依賴過多外部狀態
- 存在重複的 Dialog 組件和狀態管理

## 修復方案

### 1. 移除重複的 API 呼叫函數
- 刪除 `GetOEARecs`、`GetSoapData`、`ConvertOEARecsToTimeline` 函數
- 刪除 `RenderOEARecsTimeline` 函數
- 這些功能已由 `OEATimeLine` 組件統一處理

### 2. 優化 LabData 載入機制
```typescript
// 修改前：每次依賴變化都會重新呼叫
useEffect(() => {
    if (sDate && eDate && mrNo) {
        const fetchLabData = async () => {
            const labDataList = await GetLabData(sDate, eDate, mrNo);
            if (labDataList) {
                setLab(labDataList);
            }
        };
        fetchLabData();
    }
}, [sDate, eDate, mrNo]);

// 修改後：加入載入狀態控制，避免重複呼叫
const [labLoading, setLabLoading] = useState<boolean>(false);
const [labLoaded, setLabLoaded] = useState<boolean>(false);

useEffect(() => {
    if (sDate && eDate && mrNo && !labLoaded && !labLoading) {
        const fetchLabData = async () => {
            setLabLoading(true);
            try {
                const labDataList = await GetLabData(sDate, eDate, mrNo);
                if (labDataList) {
                    setLab(labDataList);
                }
                setLabLoaded(true);
            } catch (error) {
                console.error('載入檢驗資料失敗:', error);
            } finally {
                setLabLoading(false);
            }
        };
        fetchLabData();
    }
}, [labLoaded, labLoading]); // 移除外部狀態依賴
```

### 3. 移除重複的組件和狀態
- 刪除重複的 `opdDetail` 狀態和相關 Dialog
- 刪除重複的時間線渲染組件
- 移除不再使用的 import 和變數

### 4. 簡化初始化邏輯
```typescript
// 修改前：同時呼叫多個 API
Promise.all([
    GetMRBasic(mrNo),
    RenderOEARecsTimeline(mrNo, sDate, eDate)
])

// 修改後：只呼叫基本資料，時間線由子組件處理
GetMRBasic(mrNo).catch(error => {
    console.error('初始化基本資料失敗:', error);
});
```

## 修復效果

### 性能改善
1. **減少 API 呼叫次數**：避免重複呼叫相同的 API
2. **改善載入體驗**：加入載入狀態指示器
3. **防止重複載入**：使用載入狀態控制機制

### 代碼品質
1. **移除重複代碼**：刪除約 100 行重複的函數和組件
2. **清理未使用 import**：移除不再需要的依賴
3. **簡化組件結構**：責任分離更清晰

### 維護性提升
1. **單一責任原則**：每個組件只負責自己的 API 呼叫
2. **減少耦合度**：移除組件間的重複依賴
3. **更好的錯誤處理**：加入適當的錯誤捕獲機制

## 建議後續改善

### 1. 實作 API 快取機制
考慮使用 React Query 或 SWR 來實作更完善的快取和重新驗證機制。

### 2. 統一載入狀態管理
可以考慮使用 Context 或狀態管理庫來統一管理載入狀態。

### 3. 加入重試機制
對於網路錯誤，可以加入自動重試機制。

### 4. 性能監控
加入 API 呼叫次數和響應時間的監控，以便及時發現性能問題。
