

import { LoginReqIF } from '@/models/Login/LoginReqIF';
import { ENDPOINT, SESSION_TOKEN, USER_BASIC } from '../configs/constant';
import { apiGet, apiPost } from '@/modules/GeneralService';
import AesEncoder from '@/modules/AesEncoder';
import Cookies from 'universal-cookie';

export class AuthService {
    endpoint: string = ENDPOINT + '/auth';
    SESSION_TOKEN: string = SESSION_TOKEN;
    USER_BASIC: string = USER_BASIC;
    cookie = new Cookies();
    aes = new AesEncoder();
    // 登入
    async Login(body: LoginReqIF): Promise<any> {
        // 將userID 和PASSWORD 用AES 加密
        body.userID = this.aes.EncryptString((body.userID + "").toString());
        body.Password = this.aes.EncryptString((body.Password + "").toString());

        try {
            const response = await apiPost(this.endpoint + '/login', body);
            return response;
        } catch (err: any) {
            if (err.response && err.response.status.toString().startsWith('4')) {
                if (err.response.status === 401) {
                    console.log('認證失效，請重新登入');
                } else {
                    console.log('客戶端錯誤：', err.response.status);
                }
                throw err;
            }
            console.log('Service：', this.endpoint + '/login', err);
            // err 轉字串
            console.log(JSON.stringify(err));

            throw err;
        }

    }
    // 判斷登入狀態
    async CheckLogin(): Promise<boolean> {
        try {
            const response = await apiGet(this.endpoint + '/AuthCheck');
            if (response.status === 200) {
                console.log('有效認證');
                return true;
            }
        } catch (err) {
            // 如果 cookie 存在 就跳訊息
            if (this.cookie.get(this.SESSION_TOKEN) || this.cookie.get(this.USER_BASIC)) {
                alert('登入逾時，請重新登入');
            }
            this.cookie.remove(USER_BASIC);
            this.cookie.remove(SESSION_TOKEN);
            window.location.href = "/Login";
        }
        return false;
    }
    // async CheckLogin(): Promise<boolean> {
    //     try {
    //         await apiGet(this.endpoint + '/AuthCheck')
    //             .then((response) => {
    //                 if (response.status === 200) {
    //                     console.log('有效認證');
    //                     return true;
    //                 }
    //             })
    //             .catch((err) => {
    //                 // 如果 cookie 存在 就跳訊息
    //                 if (this.cookie.get(this.SESSION_TOKEN) || this.cookie.get(this.USER_BASIC)) {
    //                     alert('登入逾時，請重新登入');
    //                 }
    //                 this.cookie.remove(USER_BASIC);
    //                 this.cookie.remove(SESSION_TOKEN);
    //                 window.location.href = "/Login";
    //                 return false;
    //             });
    //     } catch (err) {
    //         // 如果 cookie 存在 就跳訊息
    //         if (this.cookie.get(this.SESSION_TOKEN) || this.cookie.get(this.USER_BASIC)) {
    //             alert('登入逾時，請重新登入');
    //         }
    //         this.cookie.remove(USER_BASIC);
    //         this.cookie.remove(SESSION_TOKEN);
    //         window.location.href = "/Login";
    //         return false;
    //     }
    //     return false;
    // }


}