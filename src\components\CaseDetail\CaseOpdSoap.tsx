'use client'
import { DrugIF, MRDataService, OpdSummaryIF, OrderIF } from '@/services/MRDataService';
import React, { useEffect } from 'react';
import { Button } from 'primereact/button';
import { InputTextarea } from 'primereact/inputtextarea';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { ProgressSpinner } from 'primereact/progressspinner';
import Spinner from '@/components/Spinner';


interface CaseOpdSoapProps {
    seeDate: string;
    pnc: string;
    room: string;
    seqNo: number;
}



const CaseOpdSoap: React.FC<CaseOpdSoapProps> = (props) => {
    const _mrDataService = new MRDataService();
    const [opdSummary, setOpdSummary] = React.useState<OpdSummaryIF | null>(null);
    const [loading, setLoading] = React.useState(true);
    useEffect(() => {
        // set opdSummary

        fetchData();
    }, []);

    const fetchData = async () => {
        const result: OpdSummaryIF = await GetOpdSummary(props.seeDate, props.pnc, props.room, props.seqNo.toString());
        // const result: OpdSummaryIF = await GetOpdSummary('1131009', '2', '3013', '33');
        setOpdSummary(result);
        setLoading(false);

    };
    // GetOpdSummary
    async function GetOpdSummary(seeDate: string, pnc: string, room: string, regNo: string) {
        try {
            const data = await _mrDataService.GetOpdSummary(seeDate, pnc, room, regNo);
            // console.log('OpdSummary', data);
            return data;
        } catch (error) {
            console.log(error);
        }
    }

    async function Test() {
        // GetOpdSummary  1131009	2	3013	33
        // const result: OpdSummaryIF = await GetOpdSummary('1131009', '2', '3013', '33');
        // alert(JSON.stringify(result));
        fetchData();
    }


    return (
        <div>
            {loading && <Spinner overlay={true} />}
            {/* <Button label="Test" onClick={Test} /> */}
            <div className='flex flex-col lg:flex-row border-gray-500 border-1 border-solid lg:!h-[76vh] '>
                {/* 左 */}
                <div id='soap_p1' className='w-full lg:!w-2/5 border-gray-500 border-1 border-solid mb-6'>
                    {/* 主訴 */}
                    <label className="flex items-center justify-center font-bold">S</label>
                    <InputTextarea value={opdSummary?.Subjective} className='w-full gap-1 custom_scrollbar text-lg lg:!h-[28%] min-h-[10%]' />
                    {/* 客觀 */}
                    <label className="flex items-center justify-center font-bold">O</label>
                    <InputTextarea value={opdSummary?.Objective} className='w-full gap-1 custom_scrollbar text-lg lg:!h-[37%] min-h-[10%]' />
                    {/* 診斷 */}
                    <label className="flex items-center justify-center font-bold">Diagnosis</label>
                    <DataTable value={opdSummary?.Diagnosis} className='w-full gap-1 custom_scrollbar overflow-auto max-h-[30%]  '>
                        <Column field="Diag9Code" header="ICD9" className=' font-semibold text-base py-1' headerClassName='py-1' />
                        <Column field="Diag10Code" header="ICD10" className=' font-semibold text-base py-1' headerClassName='py-1' />
                        <Column field="Diag10Name" header="診斷" className=' font-semibold text-base py-1' headerClassName='py-1' />
                    </DataTable>
                </div>
                {/* 右 */}
                <div id='soap_p2' className='w-full lg:!w-3/5 border-gray-500 border-1 border-solid '>
                    {/* 藥品 */}
                    <div>
                        <label className="flex items-center justify-center font-bold">Drug</label>
                        <DataTable value={opdSummary?.Drugs} className='w-full custom_scrollbar lg:!h-[40%]' emptyMessage={'無資料'}>
                            <Column field="Seq" header="序" className='py-1' headerClassName='py-1' />
                            <Column field="DrgName" header="藥名" className='py-1' headerClassName='py-1' />
                            <Column header="天" className='py-1' body={(rowData: DrugIF) => (
                                <span >{rowData.ClaimDay + rowData.SelfDay}</span>
                            )} headerClassName='py-1' />
                            <Column header="用量" className='py-1' body={(rowData) => (
                                <span >{rowData.Dose} {rowData.Unit}</span>
                            )} headerClassName='py-1' />
                            <Column field="Freq" header="頻率" className='py-1' headerClassName='py-1' />
                            <Column field="Route" header="途徑" className='py-1' headerClassName='py-1' />
                        </DataTable>
                    </div>
                    <div>
                        {/* 醫令 */}
                        <label className="flex items-center justify-center font-bold">Order</label>
                        <DataTable value={opdSummary?.Orders} className='w-full custom_scrollbar lg:!h-[40%]' emptyMessage={'無資料'}>
                            <Column field="SeqNo" header="序" className='py-1' headerClassName='py-1' />
                            <Column field="OrdPriceNo" header="醫令" className='py-1' headerClassName='py-1' />
                            <Column field="OrdPriceName" header="名稱" className='py-1' headerClassName='py-1' />
                            <Column header="天" className='py-1' body={(rowData: OrderIF) => (
                                <span >{rowData.ClaimDay + rowData.SelfDay}</span>
                            )} headerClassName='py-1' />
                            <Column header="用量" className='py-1' body={(rowData) => (
                                <span >{rowData.Dose} {rowData.UseUnit}</span>
                            )} headerClassName='py-1' />
                            <Column field="OrdDate" header="開單日期" className='py-1' headerClassName='py-1' />
                            <Column field="OrdTime" header="開單時間" className='py-1' headerClassName='py-1' />
                            {opdSummary?.Room.toString().toUpperCase().startsWith('ER') &&
                                <Column field="OrdTime" header="開單醫師" className='py-1' headerClassName='py-1' />}
                        </DataTable>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CaseOpdSoap;