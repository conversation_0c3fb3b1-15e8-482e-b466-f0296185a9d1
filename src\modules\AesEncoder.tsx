import { enc, AES } from 'crypto-js';
import * as crypto from 'crypto';



const key = enc.Utf8.parse("000T000Z000U0C0000H000000000000I");
const iv = enc.Utf8.parse("C000AN00C00E0R00");

function aesEncrypt(text: string): string {
    const encrypted = AES.encrypt(text, key, { iv });
    return encrypted.toString();
}

function aesDecrypt(ciphertext: string): string {
    const decrypted = AES.decrypt(ciphertext, key, { iv });
    return decrypted.toString(enc.Utf8);
}

export { aesEncrypt, aesDecrypt };


class AesEncoder {

    private _Key: string;

    constructor() {
        this._Key = "5A77E83765A5BAB37EE2673169B61878";
    }

    public DecryptString(cipherText: string): string {
        return this.DecryptStringCustom(cipherText, this._Key, "");
    }

    public DecryptStringCustom(cipherText: string, key: string, iv: string = ""): string {
        if (!cipherText) {
            return "";
        }
        if (!key.trim()) {
            key = this._Key;
        }
        if (key.length < 32) {
            throw new Error("Key length is not enough.");
        }
        // cipher
        let cipherBytes = Buffer.from(cipherText, "base64");
        // key
        const keyBytes = Buffer.from(key, "utf8");
        // iv 如果沒有傳入IV  則抓cipherBytes 前16個byte
        let ivBytes: Buffer;
        if (!iv.trim()) {
            // 抓cipherBytes 前16個byte
            ivBytes = cipherBytes.subarray(0, 16);
            cipherBytes = cipherBytes.subarray(16);
        } else {
            ivBytes = Buffer.from(iv, "utf8");
        }
        // decrypt
        const aes = crypto.createDecipheriv("aes-256-cbc", keyBytes, ivBytes);
        let decrypted = aes.update(cipherBytes);
        decrypted = Buffer.concat([decrypted, aes.final()]);
        return decrypted.toString("utf8");
    }

    public EncryptString(cipherText: string): string {
        return this.EncryptStringCustom(cipherText, this._Key, "");
    }

    public EncryptStringCustom(cipherText: string, key: string, iv: string = ""): string {
        if (!cipherText) {
            return "";
        }
        if (!key.trim()) {
            throw new Error("Key is missing.");
        }
        if (key.length < 32) {
            throw new Error("Key length is not enough.");
        }
        // key
        const keyBytes = Buffer.from(key, "utf8");
        // iv
        let ivBytes: Buffer;
        if (!iv.trim()) {
            ivBytes = crypto.randomBytes(16);
        } else {
            ivBytes = Buffer.from(iv, "utf8");
        }
        // cipher
        const cipher = crypto.createCipheriv("aes-256-cbc", keyBytes, ivBytes);
        let encrypted = cipher.update(cipherText, "utf8", "base64");
        encrypted += cipher.final("base64");
        // return
        return Buffer.concat([ivBytes, Buffer.from(encrypted, "base64")]).toString("base64");
    }
}
export default AesEncoder;