'use client';

import { useState } from 'react';

export default function TestPage1() {
    const [count, setCount] = useState(0);

    return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
            <h1 className="text-2xl font-bold mb-4">測試頁面 1</h1>
            <div className="flex items-center gap-4">
                <button
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    onClick={() => setCount(count - 1)}
                >
                    減少
                </button>
                <span className="text-xl">{count}</span>
                <button
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    onClick={() => setCount(count + 1)}
                >
                    增加
                </button>
            </div>
        </div>
    );
}
