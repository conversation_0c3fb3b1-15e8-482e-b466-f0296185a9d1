import { CancerBasicCondition, MRDataCondition } from '@/models/Conditions';
import { ENDPOINT, SESSION_TOKEN, USER_BASIC } from '../configs/constant';
import { apiGet, apiPost } from '@/modules/GeneralService';
import Cookies from 'universal-cookie';
import AesEncoder from '@/modules/AesEncoder';
import { CancerBasicIF, MRBasicIF, NucReportIF, OperationIF, RadReportIF, SpecialExamReportIF } from '@/models/MRData';

interface OeRecIF {
    chDate: string;       // 出診日期
    chPNC: string;        // 診別
    chRoom: string;       // 診室
    intRegNo: number;     // 註冊號碼
    chSecNo: string;      // 科別編號
    chSecName: string;    // 科別名稱
    chDocNo: string;      // 醫生編號
    chDocName: string;    // 醫生名稱
}

interface AdmRecIF {
    chAd1Date: string;    // 住院日期
    chAd1DateC: string;   // 出院日期
    chSecName: string;    // 科別名稱
    chDocName: string;    // 醫生名稱
    chAd1Bid: string;     // 床號
}

export interface OEARecsIF {
    OERecs: OeRecIF[];      // 門診紀錄
    AdmRecs: AdmRecIF[];    // 住院紀錄
}
export interface SoapDataIF {
    SeeDate: string;      // 看診日期
    SecName: string;      // 科別名稱
    DocName: string;      // 醫師姓名
    Icd10No1: string;     // ICD-10診斷碼1
    Icd10No2: string;     // ICD-10診斷碼2
    Icd10No3: string;     // ICD-10診斷碼3
    Icd10Name1: string;   // ICD-10診斷名稱1
    Icd10Name2: string;   // ICD-10診斷名稱2
    Icd10Name3: string;   // ICD-10診斷名稱3
    Subjective: string;   // 主訴症狀
    Objective: string;    // 客觀檢查
}


export interface OpdSummaryIF {
    SeeDate: string;      // 看診日期
    PNC: string;         // 診別
    Room: string;        // 診室
    RegNo: number;       // 掛號號碼
    MRNo: string;        // 病歷號碼
    PatientName: string; // 病人姓名
    DoctorNo: string;    // 醫師編號
    DoctorName: string;  // 醫師姓名
    SecNo: string;       // 科別編號
    SecName: string;     // 科別名稱
    Subjective: string;  // 主訴症狀
    Objective: string;   // 客觀檢查
    Diagnosis: DiagnosisIF[]; // 診斷資料陣列
    Drugs: DrugIF[];     // 用藥資料陣列
    Orders: OrderIF[];   // 醫囑資料陣列
}

export interface DiagnosisIF {
    Seq: number;         // 序號
    Diag9Code: string;   // ICD-9診斷碼
    Diag9Name: string;   // ICD-9診斷名稱
    Diag10Code: string;  // ICD-10診斷碼
    Diag10Name: string;  // ICD-10診斷名稱
}

export interface DrugIF {
    Seq: number;         // 序號
    OrdDate: string;     // 開立日期
    DrgName: string;     // 藥品名稱
    DrgPriceNo: string;  // 藥品代碼
    Dose: number;        // 劑量
    Unit: string;        // 單位
    Freq: string;        // 頻率
    Route: string;       // 給藥途徑
    ClaimDay: number;    // 申報天數
    SelfDay: number;     // 自費天數
    ClaimTotQty: number; // 申報總量
    SelfTotQty: number;  // 自費總量
    ClaimTotAmt: number; // 申報總金額
    SelfTotAmt: number;  // 自費總金額
}
export interface OrderIF {
    SeqNo: number;       // 序號
    OrdPriceNo: string;  // 醫令代碼
    OrdPriceName: string;// 醫令名稱
    Dose: number;        // 劑量
    UseUnit: string;     // 使用單位
    ClaimDay: number;    // 申報天數
    SelfDay: number;     // 自費天數
    ClaimTotQty: number; // 申報總量
    SelfTotQty: number;  // 自費總量
    OrdDate: string;     // 開立日期
    OrdTime: string;     // 開立時間
    OrdDrNo: string;     // 開立醫師代號
    OrdType: string;     // 醫令類型
    LabUNo: string;      // 檢驗單號
    ReqNo: string;       // 檢驗號
    RptStatus: string;   // 報告狀態
    ExeDrNo: string;     // 執行醫師代號
}
export interface LabDataIF {
    MRNo: string;                // 病歷號碼
    ApplyDateTime: string;       // 申請日期時間
    CONFIRMED_DateTime: string;   // 確認日期時間
    LAB_GroupHName: string;      // 群組
    LAB_TeamName: string;        // 組別
    LAB_SpecimenName: string;    // 檢體
    SPECIMEN_NAME: string;       // 檢驗名稱
    ResultValue: string;         // 檢驗結果值
    Unit: string;                // 單位
    LAB_ABNORMAL_FLAG: string;   // 異常註記
    OrdNo: string;               // 醫令代碼
    Comment: string;             // 備註
}

// 出院病摘
export interface DischargeNoteIF {
    PatName: string      // 病人姓名
    IDNo: string         // 身分證號
    BirthDate: string   // 出生日期
    MRNo: string        // 病歷號碼
    DisTrnInHos: string // 住院處入院
    Address: string     // 地址
    AdmDate: string     // 住院日期
    TrnBed: string      // 床號
    DischargeDate: string // 出院日期
    DeptNo: string      // 科別代碼
    DeptName: string    // 科別名稱
    ChiefComplaint: string // 主訴
    Major: string       // 主要診斷
    PhysicalChk: string
    CheckRpt: string    // 檢查報告
    RadRpt: string      // 放射報告
    InvasiveTreatments: string // 侵入性治療
    Consult: string      // 會診
    Opration: string    // 手術
    PathRpt: string     // 病理報告
    TreatProgress: string // 治療過程
    Instructions: string // 醫囑
    Others: string      // 其他
    Prognosis: string   // 預後
    AdmDiagCode1: string // 住院診斷碼1
    AdmDiagName1: string // 住院診斷名稱1
    AdmDiagCode2: string // 住院診斷碼2
    AdmDiagName2: string // 住院診斷名稱2
    DisDiagCode1: string // 出院診斷碼1
    DisDiagName1: string // 出院診斷名稱1
    DisDiagCode2: string // 出院診斷碼2
    DisDiagName2: string // 出院診斷名稱2
    DisDiagCode3: string // 出院診斷碼3
    DisDiagName3: string // 出院診斷名稱3
    DisDiagCode4: string // 出院診斷碼4
    DisDiagName4: string // 出院診斷名稱4
    DisDiagCode5: string // 出院診斷碼5
    DisDiagName5: string // 出院診斷名稱5
    RecDrID: string
    RecDrName: string
    VSID: string        // 主治醫師代號
    VSName: string      // 主治醫師姓名
}



export class MRDataService {

    endpoint: string = ENDPOINT + '/MRData';
    SESSION_TOKEN: string = SESSION_TOKEN;
    USER_BASIC: string = USER_BASIC;
    cookie = new Cookies();
    aes = new AesEncoder();

    // CancerBasicCondition 加密
    async aesEncryptCancerBasicCondition(contition: CancerBasicCondition) {
        if (contition.mrNoOrName) {
            contition.mrNoOrName = this.aes.EncryptString((contition.mrNoOrName + "").toString());
        }
        if (contition.cancerEName) {
            contition.cancerEName = contition.cancerEName.map((v) => this.aes.EncryptString(v));
        }
        if (contition.mrNo) {
            contition.mrNo = this.aes.EncryptString((contition.mrNo + "").toString());
        }
        if (contition.name) {
            contition.name = this.aes.EncryptString((contition.name + "").toString());
        }
        if (contition.username) {
            contition.username = this.aes.EncryptString((contition.username + "").toString());
        }
        return contition;
    }
    // MRDataCondition 加密
    async aesEncryptMRDataCondition(contition: MRDataCondition): Promise<MRDataCondition> {
        if (contition.sDate) {
            contition.sDate = this.aes.EncryptString((contition.sDate + "").toString());
        }
        if (contition.eDate) {
            contition.eDate = this.aes.EncryptString((contition.eDate + "").toString());
        }
        if (contition.mrNo) {
            contition.mrNo = this.aes.EncryptString((contition.mrNo + "").toString());
        }
        return contition;
    }


    // 取得癌症病患清單
    async GetCancerMRList(contition: CancerBasicCondition): Promise<CancerBasicIF[]> {
        try {
            contition = await this.aesEncryptCancerBasicCondition(contition);
            const response = await apiPost(this.endpoint + '/GetCancerMRList', contition);
            return response.data;
        } catch (err) { throw err; }
    }
    // 查詢病患基本資料
    // [HttpGet("GetMRBasicByMRNoOrName")]
    async GetMRBasicByMRNoOrName(mrNoOrName: string): Promise<MRBasicIF[]> {
        try {
            const encryptedMRNoOrName = this.aes.EncryptString(mrNoOrName);
            const response = await apiGet(this.endpoint + '/GetMRBasicByMRNoOrName', { MRNoOrName: encryptedMRNoOrName });
            return response.data;
        } catch (err) { throw err; }
    }

    // 取得門急住紀錄
    // [HttpGet("GetOEARecs")]
    async GetOEARecs(mrDataCondition: MRDataCondition): Promise<OEARecsIF> {
        try {
            const encryptedMRDataCondition = await this.aesEncryptMRDataCondition(mrDataCondition);
            const response = await apiGet(this.endpoint + '/GetOEARecs',
                { SDate: encryptedMRDataCondition.sDate, EDate: encryptedMRDataCondition.eDate, MRNo: encryptedMRDataCondition.mrNo });
            return response.data;
        } catch (err) { throw err; }
    }
    // 取得單次門診資料
    // [HttpGet("GetOpdSummary")]
    async GetOpdSummary(seeDate: string, pnc: string, room: string, regNo: string) {
        try {
            const encryptedSeeDate = this.aes.EncryptString(seeDate);
            const encryptedPnc = this.aes.EncryptString(pnc);
            const encryptedRoom = this.aes.EncryptString(room);
            const encryptedRegNo = this.aes.EncryptString(regNo);
            const response = await apiGet(this.endpoint + '/GetOpdSummary',
                { seeDate: encryptedSeeDate, pnc: encryptedPnc, room: encryptedRoom, regNo: encryptedRegNo });
            return response.data;
        } catch (err) { throw err; }
    }
    // 取得出院病摘
    // [HttpGet("GetDischargeNote")]
    async GetDischargeNote(mrDataCondition: MRDataCondition): Promise<DischargeNoteIF[]> {
        try {
            const encryptedMRDataCondition = await this.aesEncryptMRDataCondition(mrDataCondition);
            const response = await apiGet(this.endpoint + '/GetDischargeNote',
                { SDate: encryptedMRDataCondition.sDate, EDate: encryptedMRDataCondition.eDate, MRNo: encryptedMRDataCondition.mrNo });
            return response.data;
        } catch (err) { throw err; }
    }
    // 依 看診日 取得門診SOAP
    async SoapData(mrDataCondition: MRDataCondition): Promise<SoapDataIF[]> {
        try {
            const encryptedMRDataCondition = await this.aesEncryptMRDataCondition(mrDataCondition);
            const response = await apiGet(this.endpoint + '/SoapData', {
                SDate: encryptedMRDataCondition.sDate,
                EDate: encryptedMRDataCondition.eDate,
                MRNo: encryptedMRDataCondition.mrNo
            });
            return response.data;
        } catch (err) { throw err; }
    }
    // 取得住院用藥
    async GetAdmDrg(SDate: string, EDate: string, MRNo: string) {
        try {
            const encryptedSDate = this.aes.EncryptString(SDate);
            const encryptedEDate = this.aes.EncryptString(EDate);
            const encryptedMRNo = this.aes.EncryptString(MRNo);
            const response = await apiGet(this.endpoint + '/GetAdmDrg', { SDate: encryptedSDate, EDate: encryptedEDate, MRNo: encryptedMRNo });
            return response.data;
        } catch (err) { throw err; }
    }
    // 取得門診用藥
    async GetOpdDrg(SDate: string, EDate: string, MRNo: string) {
        try {
            const encryptedSDate = this.aes.EncryptString(SDate);
            const encryptedEDate = this.aes.EncryptString(EDate);
            const encryptedMRNo = this.aes.EncryptString(MRNo);
            const response = await apiGet(this.endpoint + '/GetOpdDrg', { SDate: encryptedSDate, EDate: encryptedEDate, MRNo: encryptedMRNo });
            return response.data;
        } catch (err) { throw err; }
    }
    // 取得檢驗報告
    async GetLabData(SDate: string, EDate: string, MRNo: string, byVerDate: string = "0", MRNoIE: string[] | null = null) {
        try {
            const encryptedSDate = this.aes.EncryptString(SDate);
            const encryptedEDate = this.aes.EncryptString(EDate);
            const encryptedMRNo = this.aes.EncryptString(MRNo);
            const encryptedMRNoIE = MRNoIE ? MRNoIE.map((v) => this.aes.EncryptString(v)) : null;
            const response = await apiGet(this.endpoint + '/LabData', {
                SDate: encryptedSDate,
                EDate: encryptedEDate,
                MRNo: encryptedMRNo,
                byVerDate: byVerDate,
                MRNoIE: encryptedMRNoIE
            });
            return response.data;
        } catch (err) { throw err; }
    }
    // 依 申請日 取得檢驗報告(只回傳已發報告的結果)
    async LabReport(SDate: string, EDate: string, MRNo: string) {
        try {
            const encryptedSDate = this.aes.EncryptString(SDate);
            const encryptedEDate = this.aes.EncryptString(EDate);
            const encryptedMRNo = this.aes.EncryptString(MRNo);
            const response = await apiGet(this.endpoint + '/LabReport', { SDate: encryptedSDate, EDate: encryptedEDate, MRNo: encryptedMRNo });
            return response.data;
        } catch (err) { throw err; }
    }
    // 依 報告確認日 取得病理報告 PathologyReport
    async PathologyReport(SDate: string, EDate: string, MRNo: string) {
        try {
            const encryptedSDate = this.aes.EncryptString(SDate);
            const encryptedEDate = this.aes.EncryptString(EDate);
            const encryptedMRNo = this.aes.EncryptString(MRNo);
            const response = await apiGet(this.endpoint + '/PathologyReport', { SDate: encryptedSDate, EDate: encryptedEDate, MRNo: encryptedMRNo });
            return response.data;
        } catch (err) { throw err; }
    }
    // 依 收件日 取得特殊檢查報告 SpecialExamReport
    async SpecialExamReport(SDate: string, EDate: string, MRNo: string): Promise<SpecialExamReportIF[]> {
        try {
            const encryptedSDate = this.aes.EncryptString(SDate);
            const encryptedEDate = this.aes.EncryptString(EDate);
            const encryptedMRNo = this.aes.EncryptString(MRNo);
            const response = await apiGet(this.endpoint + '/SpecialExamReport', { SDate: encryptedSDate, EDate: encryptedEDate, MRNo: encryptedMRNo });
            return response.data;
        } catch (err) { throw err; }
    }
    // 依 收件日 取得核醫報告
    async NucReport(SDate: string, EDate: string, MRNo: string): Promise<NucReportIF[]> {
        try {
            const encryptedSDate = this.aes.EncryptString(SDate);
            const encryptedEDate = this.aes.EncryptString(EDate);
            const encryptedMRNo = this.aes.EncryptString(MRNo);
            const response = await apiGet(this.endpoint + '/NucReport', { SDate: encryptedSDate, EDate: encryptedEDate, MRNo: encryptedMRNo });
            return response.data;
        } catch (err) { throw err; }
    }
    // 依 收件日 取得放射報告
    async RadReport(SDate: string, EDate: string, MRNo: string): Promise<RadReportIF[]> {
        try {
            const encryptedSDate = this.aes.EncryptString(SDate);
            const encryptedEDate = this.aes.EncryptString(EDate);
            const encryptedMRNo = this.aes.EncryptString(MRNo);
            const response = await apiGet(this.endpoint + '/RadReport', { SDate: encryptedSDate, EDate: encryptedEDate, MRNo: encryptedMRNo });
            return response.data;
        } catch (err) { throw err; }
    }
    // 依 手術日 取得手術紀錄
    async Operation(SDate: string, EDate: string, MRNo: string): Promise<OperationIF[]> {
        try {
            const encryptedSDate = this.aes.EncryptString(SDate);
            const encryptedEDate = this.aes.EncryptString(EDate);
            const encryptedMRNo = this.aes.EncryptString(MRNo);
            const response = await apiGet(this.endpoint + '/Operation', { SDate: encryptedSDate, EDate: encryptedEDate, MRNo: encryptedMRNo });
            return response.data;
        } catch (err) { throw err; }
    }







}