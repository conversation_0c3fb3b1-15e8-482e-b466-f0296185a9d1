'use client';

import React from 'react';
import { Card } from 'primereact/card';
import { useRouter } from 'next/navigation';
import { TabItemIF } from '@/components/Tab/tabIF';
import CaseSearch from './CaseSearch';


interface QuickAccessProps {
    tabPageRef: React.RefObject<{
        addTab: (content: TabItemIF) => void;
        removeTab: (tabID: string) => void;
        clearTabs: () => void;
    }>;
}

const QuickAccess: React.FC<QuickAccessProps> = ({ tabPageRef }) => {
    const router = useRouter();
    const addTab = (tab: TabItemIF) => {
        if (tabPageRef?.current) {
            tabPageRef.current.addTab(tab);
        }
    };

    const quickAccessItems = [
        {
            title: '個案查詢',
            icon: 'pi pi-bolt',
            color: 'bg-blue-500',
            bgColor: 'bg-blue-50',
            action: () => {
                const tab: TabItemIF = {
                    title: '個案查詢',
                    tabID: '個案查詢',
                    content: <CaseSearch tabPageRef={tabPageRef} />,
                };
                addTab(tab);
            },
            actionFullPage: () => {
                router.push('/FullPage?component=ChatBotCancerAssistant');
            }
        }
    ];

    return (
        <div className="quick-access-container p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {quickAccessItems.map((item, index) => (
                    <Card
                        key={index}
                        className={`quick-access-card cursor-pointer ${item.bgColor}`}
                        onClick={item.action}
                    >
                        {item.actionFullPage && (
                            <div
                                className="absolute top-2 right-2 p-2 rounded-full"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    item.actionFullPage();
                                }}
                            >
                                <i className="pi pi-external-link"></i>
                            </div>
                        )}
                        <div className="flex flex-col items-center justify-center p-4">
                            <div className={`${item.color} rounded-full p-4 mb-3`}>
                                <i className={`${item.icon} text-white text-2xl`}></i>
                            </div>
                            <h3 className="text-lg font-semibold text-center">{item.title}</h3>
                        </div>
                    </Card>
                ))}
            </div>
        </div>
    );
};

export default QuickAccess;
