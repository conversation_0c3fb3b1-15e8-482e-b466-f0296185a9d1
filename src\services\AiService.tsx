import { apiPost } from "@/modules/GeneralService";

export class AiService {
    // 去識別化
    async deidentify(originData: string[], sensitiveWords: string[]): Promise<string[]> {
        for (let i = 0; i < originData.length; i++) {
            for (let j = 0; j < sensitiveWords.length; j++) {
                if (originData[i].includes(sensitiveWords[j])) {
                    originData[i] = originData[i].replaceAll(sensitiveWords[j], '**');
                }
            }
        }
        return originData;
    }
    // 病歷摘要
    // async caseSummary(caseData: string): Promise<string> {
    //     // prompt 請幫我生成一份病歷摘要 
    //     const prompt = `
    //     請幫我生成一份病歷摘要  結果使用 <result>和 </result> 包起來
    //     內容不可提及名字
    //     病歷內容:
    //     ${caseData}
    //     `;
    //     const response = await apiPost(this.endpoint + '/caseSummary', { prompt });
    //     return response;
    // }
}