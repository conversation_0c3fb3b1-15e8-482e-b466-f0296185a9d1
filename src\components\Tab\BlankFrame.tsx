'use client';
// import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Spinner from '../Spinner';

interface BlankFrameProps {
    url?: string;
}

const BlankFrame: React.FC<BlankFrameProps> = ({ url }) => {
    // const router = useRouter();
    const [loading, setLoading] = useState(false)
    // 設定404
    url = url || '/404';
    // 在組件渲染時自動前往指定的 URL
    useEffect(() => {
        // const navigate = async () => {
        //     setLoading(true);
        //     await router.push(url?.trim() || '/');
        //     setLoading(false);
        // };
        // navigate();
    }, []);

    return (
        <>
            {loading && <Spinner overlay={false} />}
            <iframe
                src={url}
                className='w-full h-[99.5%] border-0 '

                style={{ overflow: 'hidden' }}
            />
        </>
    );
}

export default BlankFrame;