'use client';

import React, { useState } from 'react';
import { MRDataService } from '@/services/MRDataService';

export default function LabTestPage() {
    const [labData, setLabData] = useState<any>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // 創建 MRDataService 實例
    const mrDataService = new MRDataService();

    // 調用 GetLabData 方法
    const handleGetLabData = async () => {
        setLoading(true);
        setError(null);

        try {
            // 設定參數：取得 A100473139 的檢驗資料
            const SDate = '1090101';  // 開始日期
            const EDate = '1141231';  // 結束日期
            const MRNo = 'V120402974';   // 病歷號
            const byVerDate = '0';       // 版本日期
            const MRNoIE = null;         // 額外的病歷號陣列

            const result = await mrDataService.GetLabData(SDate, EDate, MRNo, byVerDate, MRNoIE);
            setLabData(result);
            console.log('檢驗資料:', result);
        } catch (err) {
            console.error('取得檢驗資料失敗:', err);
            setError(err instanceof Error ? err.message : '取得檢驗資料時發生錯誤');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="container mx-auto p-6">
            <h1 className="text-2xl font-bold mb-6">檢驗資料測試頁面</h1>

            <div className="mb-6">
                <button
                    onClick={handleGetLabData}
                    disabled={loading}
                    className="bg-blue-500 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded"
                >
                    {loading ? '載入中...' : '取得 A100473139 檢驗資料'}
                </button>
            </div>

            {error && (
                <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                    錯誤: {error}
                </div>
            )}

            {labData && (
                <div className="bg-gray-100 p-4 rounded">
                    <h2 className="text-lg font-semibold mb-2">檢驗資料結果:</h2>
                    <pre className="bg-white p-4 rounded overflow-auto text-sm">
                        {JSON.stringify(labData, null, 2)}
                    </pre>
                </div>
            )}
        </div>
    );
}
