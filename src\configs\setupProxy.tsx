// import { Application } from 'express';
// import { createProxyMiddleware } from 'http-proxy-middleware';
// import proxyConfig from './proxy.config';

// const setupProxy = (app: Application) => {
//     Object.keys(proxyConfig).forEach(context => {
//         app.use(
//             context,
//             createProxyMiddleware(proxyConfig[context])
//         );
//     });
// };

// export default setupProxy;
