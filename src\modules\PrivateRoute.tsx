// import { Navigate } from "react-router-dom";
// import React from "react";
// import Cookies from "universal-cookie";
// import { Constants } from '@/configs/constant';

// function render(c: JSX.Element) {
//     return c;
// }

// const Private = (Component: JSX.Element) => {



//     var cookie = new Cookies();
//     let isCookieUserValid = cookie.get(Constants.Cookies.UserID) !== undefined && cookie.get(Constants.Cookies.UserID) !== '' ? true : false;
//     return isCookieUserValid ? render(Component) : <Navigate to="/login" />;
// };

// export default Private;
// /*
// npm install universal-cookie
// npm install react-router-dom

// */