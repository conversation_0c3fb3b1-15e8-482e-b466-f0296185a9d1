import { useState, useEffect } from 'react';

/**
 * 自定義 Hook 用於檢測是否為行動裝置
 * @param breakpoint - 螢幕寬度斷點，預設為 768px (Tailwind md)
 * @returns boolean - 是否為行動裝置
 */
export const useIsMobile = (breakpoint: number = 768): boolean => {
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        const checkIsMobile = () => {
            // 檢查 User Agent
            const userAgent = navigator.userAgent;
            const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
            const isMobileDevice = mobileRegex.test(userAgent);
            
            // 檢查螢幕寬度
            const isSmallScreen = window.innerWidth <= breakpoint;
            
            // 檢查是否為觸控裝置
            const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            
            // 綜合判斷
            setIsMobile(isMobileDevice || (isSmallScreen && isTouchDevice) || isSmallScreen);
        };

        // 初始檢測
        checkIsMobile();

        // 監聽視窗大小變化
        const handleResize = () => {
            checkIsMobile();
        };

        window.addEventListener('resize', handleResize);
        
        // 清理事件監聽器
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, [breakpoint]);

    return isMobile;
};

/**
 * 檢測裝置類型的工具函數
 */
export const getDeviceType = () => {
    const userAgent = navigator.userAgent;
    
    if (/iPad/i.test(userAgent)) {
        return 'tablet';
    }
    
    if (/Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
        return 'mobile';
    }
    
    return 'desktop';
};

/**
 * 檢測螢幕方向
 */
export const useScreenOrientation = () => {
    const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');

    useEffect(() => {
        const checkOrientation = () => {
            setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
        };

        checkOrientation();
        window.addEventListener('resize', checkOrientation);
        
        return () => {
            window.removeEventListener('resize', checkOrientation);
        };
    }, []);

    return orientation;
};
