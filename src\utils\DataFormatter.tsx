// date string 轉成民國年月日 1954-02-07T00:00:00 轉成 43-02-07
export function convertToTWDateHyphenFromDateString(date: string): string {
    const dateObj = new Date(date);
    const year = dateObj.getFullYear() - 1911;
    const month = dateObj.getMonth() + 1;
    const day = dateObj.getDate();
    return `${year.toString()}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
}
// date  轉成民國年月日 1954-02-07T00:00:00 轉成 43-02-07
export function convertToTWDateHyphenFromDate(date: Date): string {
    const year = date.getFullYear() - 1911;
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year.toString()}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
}
// date string 轉成民國年月日 1954-02-07T00:00:00 轉成 0430207
export function convertToTWDateFromDateString(date: string): string {
    const dateObj = new Date(date);
    const year = dateObj.getFullYear() - 1911;
    const month = dateObj.getMonth() + 1;
    const day = dateObj.getDate();
    return `${year.toString().padStart(3, '0')}${month.toString().padStart(2, '0')}${day.toString().padStart(2, '0')}`;
}
// date 轉成民國年月日 1954-02-07T00:00:00 轉成 0430207
export function convertToTWDateFromDate(date: Date): string {
    const year = date.getFullYear() - 1911;
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year.toString().padStart(3, '0')}${month.toString().padStart(2, '0')}${day.toString().padStart(2, '0')}`;
}
// 民國年月日 轉成 民國  年-月-日 0430207  或  430207 轉成 43-02-07
export function convertToTWDateHyphenFromTWDateString(date: string): string {
    // 5碼 或 6碼 補到7碼
    date = date.padStart(7, '0');
    const year = date.slice(0, 3);
    const month = date.slice(3, 5);
    const day = date.slice(5, 7);
    return `${year}-${month}-${day}`;
}

