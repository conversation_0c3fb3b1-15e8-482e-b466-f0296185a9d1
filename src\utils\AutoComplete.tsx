// 自動填補身分證字號
export function getIdPossibleSuffixes(idPrefix: string, minLen?: number): string[] {
    // 預設最小長度為7
    minLen = minLen || 7;

    const idMapping: { [key: string]: number } = {
        A: 10, B: 11, C: 12, D: 13, E: 14, F: 15, G: 16, H: 17, I: 34, J: 18,
        K: 19, L: 20, M: 21, N: 22, O: 35, P: 23, Q: 24, R: 25, S: 26, T: 27,
        U: 28, V: 29, W: 32, X: 30, Y: 31, Z: 33
    };

    const weights = [1, 9, 8, 7, 6, 5, 4, 3, 2, 1, 1];

    // 驗證輸入長度
    if (idPrefix.length < minLen || idPrefix.length > 10) {
        return [];
        // throw new Error('Invalid ID length. Please provide 3 to 10 characters.');
    }

    const firstLetter = idPrefix[0];
    const firstLetterValue = idMapping[firstLetter];
    const idArray = [
        Math.floor(firstLetterValue / 10),
        firstLetterValue % 10,
        ...idPrefix.slice(1).split('').map(Number)
    ];

    const possibleSuffixes: string[] = [];

    if (idPrefix.length === 10) {
        // 當長度為10時，檢查完整身份證字號的有效性
        const total = idArray.reduce((sum, num, index) => sum + num * weights[index], 0);
        if (total % 10 === 0) {
            possibleSuffixes.push(idPrefix);
        } else {
            return [];
        }
    } else {
        // 當長度為3到9時，生成可能的組合
        const startIndex = idPrefix.length;
        const maxSuffixLength = 10 - startIndex; // 計算剩下的位數
        const maxNumber = Math.pow(10, maxSuffixLength) - 1; // 最大可能值

        for (let i = 0; i <= maxNumber; i++) {
            const suffix = i.toString().padStart(maxSuffixLength, '0');
            const checkArray = [...idArray, ...suffix.split('').map(Number)];
            const total = checkArray.reduce((sum, num, index) => sum + num * weights[index], 0);
            if (total % 10 === 0) {
                possibleSuffixes.push(idPrefix + suffix);
            }
        }
    }

    return possibleSuffixes;
}
