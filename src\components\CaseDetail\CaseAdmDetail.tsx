'use client';
import React, { useEffect, useState } from 'react';
import { InputTextarea } from 'primereact/inputtextarea';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Card } from 'primereact/card';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { Button } from 'primereact/button';
import Spinner from '@/components/Spinner';
import { DischargeNoteIF, MRDataService } from '@/services/MRDataService';

interface CaseAdmDetailProps {
    admDate: string;
    admDisDate: string;
    mrNo: string;
    isShowBasic?: boolean;
}

const CaseAdmDetail: React.FC<CaseAdmDetailProps> = ({ admDate, admDisDate, mrNo, isShowBasic = true }) => {
    const _mrDataService = new MRDataService();
    const [admData, setAdmData] = useState<DischargeNoteIF | null>(null);
    const [loading, setLoading] = useState(true);
    useEffect(() => {

        const fetchData = async () => {
            const result = await _mrDataService.GetDischargeNote({ sDate: admDisDate, eDate: admDisDate, mrNo: mrNo });
            if (result && result.length > 0) {
                setAdmData(result[0]);
            } else {
                // 查無資料
                alert('查無資料');
            }
            setLoading(false);
        };

        fetchData();

    }, []);

    if (loading) return <Spinner overlay={true} />;
    if (!admData) return <div>無資料</div>;

    // 診斷表格資料
    const diagRows = [
        { code: admData.AdmDiagCode1, name: admData.AdmDiagName1 },
        { code: admData.AdmDiagCode2, name: admData.AdmDiagName2 },
    ].filter(row => row.code || row.name);

    const disDiagRows = [
        { code: admData.DisDiagCode1, name: admData.DisDiagName1 },
        { code: admData.DisDiagCode2, name: admData.DisDiagName2 },
        { code: admData.DisDiagCode3, name: admData.DisDiagName3 },
        { code: admData.DisDiagCode4, name: admData.DisDiagName4 },
        { code: admData.DisDiagCode5, name: admData.DisDiagName5 },
    ].filter(row => row.code || row.name);


    const spanStyle = 'text-lg px-1 bg-purple-100 rounded-lg break-words';
    const textAreaStyle = 'w-full custom_scrollbar text-base min-h-[160px]';
    return (
        <div className="flex flex-col 
                        lg:flex-row border border-purple-200 rounded-xl shadow-lg bg-white items-stretch">
            {/* 左側：基本資料與主訴、現病史、身體檢查 */}
            <div className="w-full lg:w-2/5 lg:border-r border-gray-200 p-4 flex flex-col gap-4 bg-purple-50 lg:rounded-l-xl overflow-auto h-full">
                {isShowBasic && <div >
                    <label className="font-bold">基本資料</label>
                    <div className='flex flex-col gap-2 my-2' >
                        <div className={` ${spanStyle}`}>
                            <label className='font-bold'>姓名：</label>
                            <label className=''>{admData.PatName}</label>
                        </div>
                        <span className={` ${spanStyle}`}>
                            <label className='font-bold'>病歷號：</label>
                            <label className=''>{admData.MRNo}</label>
                        </span>

                        <span className={` ${spanStyle}`}>
                            <label className='font-bold'>生日：</label>
                            <label className=''>{admData.BirthDate}</label>
                        </span>
                        <span className={` ${spanStyle}`}>
                            <label className='font-bold'>住址：</label>
                            <label className='break-words'>{admData.Address}</label>
                        </span>
                        <span className={` ${spanStyle}`}>
                            <label className='font-bold'>入院日：</label>
                            <label className=''>{admData.AdmDate}</label>
                        </span>
                        <span className={` ${spanStyle}`}>
                            <label className='font-bold'>出院日：</label>
                            <label className=''>{admData.DischargeDate}</label>
                        </span>
                        <span className={` ${spanStyle}`}>
                            <label className='font-bold'>科別：</label>
                            <label className=''>{admData.DeptName}</label>
                        </span>
                        <span className={` ${spanStyle}`}>
                            <label className='font-bold'>主治醫師：</label>
                            <label className='eudc'>{admData.RecDrName}</label>
                        </span>
                    </div>
                </div>
                }

                <div>
                    <label className="font-bold">主訴</label>
                    <InputTextarea value={admData.ChiefComplaint || '無資料'} className={textAreaStyle} readOnly />
                </div>
                <div>
                    <label className="font-bold">入院診斷</label>
                    <div className="overflow-x-auto">
                        <DataTable value={diagRows} className="w-full custom_scrollbar" emptyMessage={'無資料'}>
                            <Column field="code" header="ICD" className="py-1 text-sm" headerClassName="py-1 text-sm" style={{ minWidth: '80px' }} />
                            <Column field="name" header="診斷" className="py-1 text-sm break-words" headerClassName="py-1 text-sm" style={{ minWidth: '120px' }} />
                        </DataTable>
                    </div>
                </div>
                <div>
                    <label className="font-bold">出院診斷</label>
                    <div className="overflow-x-auto">
                        <DataTable value={disDiagRows} className="w-full custom_scrollbar" emptyMessage={'無資料'}>
                            <Column field="code" header="ICD" className="py-1 text-sm" headerClassName="py-1 text-sm" style={{ minWidth: '80px' }} />
                            <Column field="name" header="診斷" className="py-1 text-sm break-words" headerClassName="py-1 text-sm" style={{ minWidth: '120px' }} />
                        </DataTable>
                    </div>
                </div>
            </div>
            {/* 右側：檢查、影像、手術、病理、治療、出院指示、診斷 */}
            <div className="w-full lg:w-3/5 p-4 flex flex-col gap-4 bg-white lg:rounded-r-xl overflow-hidden overflow-y-auto">


                <div className=' flex-1'>
                    <label className="font-bold">現病史/重要病史</label>
                    <InputTextarea value={admData.Major || '無資料'} className={textAreaStyle} readOnly />
                </div>

                <div>
                    <label className="font-bold">身體檢查</label>
                    <InputTextarea value={admData.PhysicalChk || '無資料'} className={textAreaStyle} readOnly />
                </div>
                <div>
                    <label className="font-bold">檢查報告</label>
                    <InputTextarea value={admData.CheckRpt || '無資料'} className={textAreaStyle} readOnly />
                </div>
                <div>
                    <label className="font-bold">影像報告</label>
                    <InputTextarea value={admData.RadRpt || '無資料'} className={textAreaStyle} readOnly />
                </div>
                <div>
                    <label className="font-bold">手術紀錄</label>
                    <InputTextarea value={admData.Opration || '無資料'} className={textAreaStyle} readOnly />
                </div>
                <div>
                    <label className="font-bold">病理報告</label>
                    <InputTextarea value={admData.PathRpt || '無資料'} className={textAreaStyle} readOnly />
                </div>
                <div>
                    <label className="font-bold">治療過程</label>
                    <InputTextarea value={admData.TreatProgress || '無資料'} className={textAreaStyle} readOnly />
                </div>
                <div>
                    <label className="font-bold">出院指示</label>
                    <InputTextarea value={admData.Instructions || '無資料'} className={textAreaStyle} readOnly />
                </div>
            </div>
        </div>
    );
};

export default CaseAdmDetail;
