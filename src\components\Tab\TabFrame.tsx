import React, { useState } from 'react';
import { TabView, TabPanel } from 'primereact/tabview';
import { TabItemIF } from './tabIF';


interface TabPageProps {
    tragetUrl: TabItemIF;
}

const TabPage: React.FC<TabPageProps> = ({ tragetUrl }) => {
    const [tabList, setTabList] = useState<TabItemIF[]>([{
        title: '儀錶板',
        url: 'mainPage/dashboardPage',
        tabID: 'dashboardPage'
    }]);
    const [activeIndex, setActiveIndex] = useState<number>(0);
    const notDisableTab: string[] = ['儀錶板', '個案清單'];
    const pushTab = (tab: TabItemIF) => {
        const findList = tabList.filter(ele => ele.title === tab.title);
        if (findList.length === 0) {
            setTabList([...tabList, tab]);
            setTimeout(() => setActiveIndex(tabList.length), 200);
        } else {
            if (tab.parm) {
                findList[0].parm = tab.parm;
            }
            setTimeout(() => setActiveIndex(tabList.indexOf(findList[0])), 200);
        }
    };

    React.useEffect(() => {
        const tabItem: TabItemIF = {
            title: tragetUrl.title,
            url: tragetUrl.url,
            tabID: tragetUrl.tabID,
        };

        pushTab(tabItem);
    }, [tragetUrl]);



    const closeTab = (event: any) => {
        const updatedTabList = [...tabList];
        updatedTabList.splice(event.index, 1);
        setTabList(updatedTabList);
        if (updatedTabList.length > 0) {
            setActiveIndex(updatedTabList.length - 1);
        }
        return true;
    };

    return (
        <TabView scrollable activeIndex={activeIndex} onTabClose={closeTab} onTabChange={(e: { index: number }) => setActiveIndex(e.index)}>
            {tabList.map((tab, index) => (
                <TabPanel key={index} header={tab.title} closable={notDisableTab.indexOf(tab.title) === -1}>
                    {/* <app-blank-page tragetUrl={tab.url} parm={tab.parm} /> */}
                    <TabPage tragetUrl={tab} />
                </TabPanel>
            ))}
        </TabView>
    );
};

export default TabPage;