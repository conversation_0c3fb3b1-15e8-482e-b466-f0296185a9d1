

import { LoginReqIF } from '@/models/Login/LoginReqIF';
import { ENDPOINT, SESSION_TOKEN, USER_BASIC } from '../configs/constant';
import { apiGet, apiPost } from '@/modules/GeneralService';
import { aesEncrypt } from '@/modules/AesEncoder';
import Cookies from 'universal-cookie';

export class GenService {
    endpoint: string = ENDPOINT + '/Gen';
    SESSION_TOKEN: string = SESSION_TOKEN;
    USER_BASIC: string = USER_BASIC;
    cookie = new Cookies();

    async GetCancerTypes(): Promise<any> {
        try {
            return await apiGet(this.endpoint + '/GetCancerMapping');
        } catch (err) {
            throw err;
        }
    }



}