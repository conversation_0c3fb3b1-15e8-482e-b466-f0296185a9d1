import type { Metadata } from "next";
import "./globals.css";
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import 'primereact/resources/themes/soho-light/theme.css'; // https://primereact.org/theming/

export const metadata: Metadata = {
  title: "CancerBook",
  description: "A web application for cancer management.",
  manifest: "/manifest.json",
  keywords: "cancer, management, health, PWA",
  authors: { name: "HLDDian", url: "https://yourwebsite.com" },
  icons: {
    icon: "/icons/icon-192x192.png",
    apple: "/icons/icon-192x192.png",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {

  return (
    <html lang="zh-tw">
      <head>
        <meta name="theme-color" content="#000000" />
        <link rel="manifest" href={metadata.manifest?.toString()} />
        <meta name="description" content={metadata.description || ''} />
        <meta name="keywords" content={Array.isArray(metadata.keywords) ? metadata.keywords.join(', ') : metadata.keywords || ''} />

        <link
          rel="preload"
          href="/fonts/EUDC-Tzuchi.ttf"
          as="font"
          type="font/ttf"
          crossOrigin="anonymous"
        />
      </head>
      <body>{children}</body>
    </html>
  );
}
