export interface MRBasicIF {
    MRNo?: string;
    Name?: string;
    NewName?: string;
    ID?: string;
    MKName?: string;
    Birthday?: Date;
    IfMarried?: string;
    Sex?: string;
    Blood?: string;
    InsuType?: string;
    DCTType?: string;
    TelH?: string;
    TelO?: string;
    Add1?: string;
    ZipCode1?: string;
    Add2?: string;
    ZipCode2?: string;
}
export interface CancerBasicIF {
    cancerNo: string;
    mrNo: string;
    icd9Code: string;
    initialICD9Date: string;
    firstCat: string;
    name: string;
    birthday: string;
    sex: string;
    add: string;
    death: boolean;
    tel: string;
    cancerType: string;
    cancerTypeC: string;
}


export interface SpecialExamReportIF {
    ExmName?: string;
    OrdNo?: string;
    AppDTM?: string;
    RcpDTM?: string;
    VerDTM?: string;
    Bodypart?: string;
    Diagnosis?: string;
    Report?: string;
    Subject?: string;
    Suggestion?: string;
}
export interface NucReportIF {
    ExmName?: string;
    AppDocName?: string;
    RcpDTM?: string;
    Report?: string;
}

export interface RadReportIF {
    RcpDateTime?: string;
    ExamName?: string;
    ReportContent?: string;
    OrdNo?: string;
    SerialNo?: string;
    VerDTM?: string;
}

export interface OperationIF {
    OPDate?: string;
    OPName?: string;
    Source?: string;
    OPUserName?: string;
    DeptName?: string;
    OPBDiag?: string;
    OPADiag?: string;
    Finding?: string;
    Procedure?: string;
    RecDRName?: string;
    OrCDate?: string;
    OprCaseNo?: string;
    OrCode?: string;
}