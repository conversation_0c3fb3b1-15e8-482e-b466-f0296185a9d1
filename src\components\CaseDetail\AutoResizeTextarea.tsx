'use client';
import React, { useEffect, useRef } from 'react';

interface AutoResizeTextareaProps {
    value?: string;
    className?: string;
    readOnly?: boolean;
    placeholder?: string;
}

const AutoResizeTextarea: React.FC<AutoResizeTextareaProps> = ({
    value = '',
    className = '',
    readOnly = false,
    placeholder = ''
}) => {
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    const adjustHeight = () => {
        const textarea = textareaRef.current;
        if (textarea) {
            // 重置高度以獲取正確的 scrollHeight
            textarea.style.height = 'auto';

            // 獲取實際的 scrollHeight（內容的實際高度）
            const scrollHeight = textarea.scrollHeight;

            // 設定最小高度和最大高度
            const minHeight = 48; // 最小高度（一行 + padding）
            const maxHeight = 300; // 最大高度，超過後顯示滾動條

            // 計算最終高度
            let finalHeight = Math.max(scrollHeight, minHeight);
            finalHeight = Math.min(finalHeight, maxHeight);

            // 設定高度
            textarea.style.height = `${finalHeight}px`;

            // 如果內容超過最大高度，顯示滾動條
            if (scrollHeight > maxHeight) {
                textarea.style.overflowY = 'auto';
            } else {
                textarea.style.overflowY = 'hidden';
            }
        }
    };

    useEffect(() => {
        adjustHeight();
    }, [value]);

    // 監聽視窗大小變化，重新調整高度
    useEffect(() => {
        const handleResize = () => {
            adjustHeight();
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    return (
        <textarea
            ref={textareaRef}
            value={value}
            className={`w-full custom_scrollbar text-base resize-none border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${className}`}
            readOnly={readOnly}
            placeholder={placeholder}
            style={{
                minHeight: '48px',
                lineHeight: '1.5'
            }}
        />
    );
};

export default AutoResizeTextarea; 