import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router'


interface BlankPageProps {
    tragetUrl: string;
    parm: any;
}

const TabPage: React.FC<BlankPageProps> = ({ tragetUrl, parm }) => {
    const router = useRouter();

    const navigateToTargetUrl = () => {
        if (parm) {
            router.push({
                pathname: tragetUrl,
                search: new URLSearchParams(parm).toString(),
            });
        } else {
            router.push(tragetUrl);
        }
    };

    useEffect(() => {
        navigateToTargetUrl();
    }, [parm, navigateToTargetUrl]);


    return null;
};

export default TabPage;