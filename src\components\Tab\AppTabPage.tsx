'use client';
import React, { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { TabView, TabPanel } from 'primereact/tabview';
import Dashboard from '@/components/Dashboard';
import { TabItemIF } from './tabIF';
import './AppTabPage.css';
import { ReactElement } from 'react';



const AppTabPage = forwardRef((_, ref) => {
    const initTab: TabItemIF = { title: 'Dashboard', tabID: 'Dashboard', content: <Dashboard /> };
    // const initTab: TabItemIF = { title: 'TEST', tabID: 'TEST', content: <CaseOpdSoap seeDate={'1131009'} pnc={'2'} room={'3013'} seqNo={33} /> };

    const [tabs, setTabs] = useState<TabItemIF[]>([initTab]);
    const [activeIndex, setActiveIndex] = useState(0);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(true);
    const [isShowTabHeader, setIsShowTabHeader] = useState(true);

    useEffect(() => {
        const handleBeforeUnload = (event: BeforeUnloadEvent) => {
            // 檢查是否正在登出，如果是則不攔截
            if (window.sessionStorage.getItem('isLoggingOut') === 'true') {
                return;
            }

            if (!hasUnsavedChanges) return;
            // 不使用已淘汰的 returnValue 屬性
            // 根據標準，只需要呼叫 preventDefault() 即可
            event.preventDefault();
            // 為了相容舊版瀏覽器，可以返回一個字串作為提示訊息
            return '您有未儲存的變更，確定要離開嗎？';
        };

        window.addEventListener('beforeunload', handleBeforeUnload);
        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [hasUnsavedChanges]);

    const addTab = (item: TabItemIF) => {
        setTabs(prevTabs => {
            const existingTabIndex = prevTabs.findIndex(tab => tab.tabID === item.tabID);
            const restartList = ['個案查詢'];
            const contentElement = item.content as ReactElement;
            const hasMrnoOrname = contentElement?.props?.mrNoOrName;

            if (existingTabIndex !== -1 && !prevTabs[existingTabIndex].content) {
                const newTabs = [...prevTabs];
                newTabs[existingTabIndex] = {
                    ...newTabs[existingTabIndex],
                    content: item.content
                };
                setTimeout(() => setActiveIndex(existingTabIndex), 0);
                return newTabs;
            }

            if (restartList.includes(item.tabID) && existingTabIndex !== -1 && hasMrnoOrname) {
                const newTabs = [...prevTabs];
                newTabs[existingTabIndex] = {
                    ...newTabs[existingTabIndex],
                    content: item.content
                };
                setTimeout(() => setActiveIndex(existingTabIndex), 0);
                return newTabs;
            }
            if (existingTabIndex !== -1) {
                setTimeout(() => setActiveIndex(existingTabIndex), 0);
                return prevTabs;
            }
            setTimeout(() => setActiveIndex(prevTabs.length), 0);
            return [...prevTabs, item];
        });
    };

    const removeTab = (tabID: string) => {
        const updatedTabs = tabs.filter(tab => tab.tabID !== tabID);
        setTabs(updatedTabs);
        setActiveIndex(0);
    };

    const handleCloseTab = (tabID: string) => {
        setTabs(prevTabs => {
            const newTabs = prevTabs.filter(tab => tab.tabID !== tabID);
            if (newTabs.length <= activeIndex) {
                setActiveIndex(newTabs.length - 1);
            }
            return newTabs;
        });
        return false;
    };

    const clearTabs = () => {
        setTabs([initTab]);
        setActiveIndex(0);
    };

    useImperativeHandle(ref, () => ({
        addTab,
        removeTab,
        clearTabs,
        tabs,
        setTabs,
        setIsShowTabHeader,
        isShowTabHeader
    }));

    // const getTabs = () => {
    //     return tabs;
    // }

    return (
        // 桌上型電腦、行動裝置都自動填滿寬高
        <div className="h-full w-full ">
            {/* <Button label="Clear Tabs" onClick={clearTabs} /> */}
            <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)} renderActiveOnly={false}
                onBeforeTabClose={(e) => handleCloseTab(tabs[e.index].tabID)} scrollable={true}
                pt={{
                    navContainer: {
                        className: `${isShowTabHeader ? '' : 'hidden'}`
                    },
                    panelContainer: {
                        className: 'h-full p-0 '
                    },
                }}
            >
                {/* {tabs.map((tab, index) => (
                    <TabPanel key={index} header={tab.title} closable={tab.title !== initTab.title}
                    >
                        <div className="h-[85vh] overflow-auto">
                            {tab.content}
                        </div>
                    </TabPanel>
                ))} */}
                {tabs.map((tab) => (
                    <TabPanel key={tab.tabID} header={tab.title} closable={tab.title !== initTab.title} >
                        <div className="overflow-y">
                            {tab.content}
                        </div>
                    </TabPanel>
                ))}
            </TabView>
        </div>
    );
});
AppTabPage.displayName = 'AppTabPage';

export default AppTabPage;
