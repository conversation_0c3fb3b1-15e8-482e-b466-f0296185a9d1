import React, { createContext, useRef, useContext } from 'react';

// 定義 context 的型別
export interface TabPageContextType {
    tabPageRef: React.MutableRefObject<{
        addTab: (title: string, content: React.ReactNode) => void;
        removeTab: (tabID: string) => void;
        clearTabs: () => void;
    } | null>;
}

// 創建 context
export const TabPageContext = createContext<TabPageContextType | undefined>(undefined);

export const TabPageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const tabPageRef = useRef<{
        addTab: (title: string, content: React.ReactNode) => void;
        removeTab: (tabID: string) => void;
        clearTabs: () => void;
    } | null>(null);

    return (
        <TabPageContext.Provider value={{ tabPageRef }}>
            {children}
        </TabPageContext.Provider>
    );
};

// 自定義 Hook，方便使用
export const useTabPageContext = () => {
    const context = useContext(TabPageContext);
    if (!context) {
        throw new Error('useTabPageContext must be used within a TabPageProvider');
    }
    return context;
};
