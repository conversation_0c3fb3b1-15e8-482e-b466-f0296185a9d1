import withPWA from "next-pwa";

/** @type {import('next').NextConfig} */
const nextConfig = {
    trailingSlash: true,
    reactStrictMode: true,
    swcMinify: true,
    images: {
        unoptimized: true,
        domains: ["*"],
    },
    async rewrites() {
        return [
            {
                source: '/api/:path*',
                destination: 'http://************:5015/api/:path*',
            },
        ];
    },
};

export default withPWA({
    dest: "public",
    disable: process.env.NODE_ENV === "development",
    // disable: false,
    register: true,
    skipWaiting: true,
})(nextConfig);