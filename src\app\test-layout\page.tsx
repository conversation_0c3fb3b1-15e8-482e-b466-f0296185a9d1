'use client';
import React from 'react';
import CaseAdmDetail from '@/components/CaseDetail/CaseAdmDetail';

// 測試用的模擬資料
const mockData = {
    admDate: '1130124',
    admDisDate: '1130126',
    mrNo: 'A100040881',
    isShowBasic: true
};

export default function TestLayoutPage() {
    return (
        <div className="min-h-screen bg-gray-100">
            <div className="container mx-auto py-8">
                <div className="mb-6 text-center">
                    <h1 className="text-3xl font-bold text-gray-800 mb-2">
                        CaseAdmDetail 佈局測試頁面
                    </h1>
                    <p className="text-gray-600">
                        請在不同螢幕尺寸下測試此佈局的響應式效果
                    </p>
                    <div className="mt-4 flex flex-wrap justify-center gap-4 text-sm">
                        <div className="bg-blue-100 px-3 py-1 rounded">
                            <span className="font-semibold">桌面版:</span> ≥1024px (改良的2欄佈局)
                        </div>
                        <div className="bg-green-100 px-3 py-1 rounded">
                            <span className="font-semibold">平板版:</span> 768px-1023px (2欄佈局)
                        </div>
                        <div className="bg-orange-100 px-3 py-1 rounded">
                            <span className="font-semibold">行動版:</span> &lt;768px (1欄佈局 + 摺疊)
                        </div>
                    </div>
                </div>

                <CaseAdmDetail
                    admDate={mockData.admDate}
                    admDisDate={mockData.admDisDate}
                    mrNo={mockData.mrNo}
                    isShowBasic={mockData.isShowBasic}
                />

                <div className="mt-8 p-6 bg-white rounded-lg shadow-sm">
                    <h2 className="text-xl font-bold mb-4">測試檢查清單</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="space-y-2">
                            <h3 className="font-semibold text-gray-700">桌面版 (≥1024px)</h3>
                            <ul className="text-sm space-y-1">
                                <li>✓ 改良的2欄佈局</li>
                                <li>✓ 左欄：主訴+診斷+身體檢查</li>
                                <li>✓ 右欄：現病史（更大空間）</li>
                                <li>✓ 下方：報告區域2欄排列</li>
                                <li>✓ 更好的空間利用率</li>
                            </ul>
                        </div>
                        <div className="space-y-2">
                            <h3 className="font-semibold text-gray-700">平板版 (768px-1023px)</h3>
                            <ul className="text-sm space-y-1">
                                <li>✓ 2欄網格佈局</li>
                                <li>✓ 適中的資訊密度</li>
                                <li>✓ 觸控友善的元素</li>
                                <li>✓ 良好的可讀性</li>
                            </ul>
                        </div>
                        <div className="space-y-2">
                            <h3 className="font-semibold text-gray-700">行動版 (&lt;768px)</h3>
                            <ul className="text-sm space-y-1">
                                <li>✓ 1欄垂直佈局</li>
                                <li>✓ 基本資料可摺疊</li>
                                <li>✓ 詳細報告可摺疊</li>
                                <li>✓ 不需要縮放即可閱讀</li>
                                <li>✓ 觸控友善的按鈕大小</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
