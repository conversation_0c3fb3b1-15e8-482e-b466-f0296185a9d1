import React, { FC, useCallback, useEffect, useRef, useState, useMemo } from 'react';
import { Timeline } from 'primereact/timeline';
import { Card } from 'primereact/card';
import { DischargeNoteIF, MRDataService, OEARecsIF, SoapDataIF } from '@/services/MRDataService';
import { convertToTWDateFromDate, convertToTWDateHyphenFromTWDateString } from '@/utils/DataFormatter';
import { showError } from '@/components/Toast';
import { MRDataCondition } from '@/models/Conditions';
import { Button } from 'primereact/button';
import CaseOpdSoap from './CaseOpdSoap';
import { Dialog } from 'primereact/dialog';
import { MRBasicIF } from '@/models/MRData';
import CaseAdmDetail from './CaseAdmDetail';
import { ProgressSpinner } from 'primereact/progressspinner';

interface OEATimeLineProps {
    sDate?: string;
    eDate?: string;
    mrNo: string;

}
interface OpdDetailParamIF {
    seeDate: string;
    pnc: string;
    room: string;
    seqNo: number;
}
interface AdmDetailParamIF {
    admDate: string;
    admDisDate: string;
    mrNo: string;
}

interface TimelineEventIF {
    opdType?: string;
    title?: string;
    subTitle?: string;
    content?: string;
    icon?: string;
    color?: string;
    image?: string;
    tmId?: string;
    hospType?: string;
}

const OEATimeLine: FC<OEATimeLineProps> = ({ mrNo, sDate, eDate }) => {
    const [timelineEvents, setTimelineEvents] = useState<TimelineEventIF[]>([]);
    const [opdDetail, setOpdDetail] = useState<OpdDetailParamIF | null>(null);
    const [admDetail, setAdmDetail] = useState<AdmDetailParamIF | null>(null);
    const [mrBasic, setMrBasic] = useState<MRBasicIF | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [initialized, setInitialized] = useState(false);
    const timelineWrapperRef = useRef<HTMLDivElement>(null);
    const timelineRef = useRef<HTMLDivElement>(null);

    // 使用 useMemo 創建 MRDataService 實例，避免重複創建
    const _mrDataService = useMemo(() => new MRDataService(), []);

    // 計算日期範圍，避免重複計算
    const { startDate, endDate } = useMemo(() => {
        let start = sDate;
        if (!start) {
            const today = new Date();
            const fiveYearsAgo = new Date(today.getFullYear() - 5, today.getMonth(), today.getDate());
            start = convertToTWDateFromDate(fiveYearsAgo);
        }

        let end = eDate;
        if (!end) {
            end = convertToTWDateFromDate(new Date());
        }

        return { startDate: start, endDate: end };
    }, [sDate, eDate]);

    // 用mrno 抓MRBasic - 只執行一次
    useEffect(() => {
        if (!initialized && mrNo) {
            setInitialized(true);
            const fetchMRBasic = async () => {
                try {
                    const result = await _mrDataService.GetMRBasicByMRNoOrName(mrNo);
                    if (result && result.length > 0) {
                        setMrBasic(result[0]);
                    }
                } catch (error) {
                    showError('取得病患資料失敗');
                    console.error(error);
                }
            };

            fetchMRBasic();
        }
    }, [mrNo, initialized, _mrDataService]);

    // ConvertOEARecsToTimeline 
    const ConvertOEARecsToTimeline = useCallback((OEARecs: OEARecsIF, soapDataList: SoapDataIF[], dischargeNoteList: DischargeNoteIF[]): TimelineEventIF[] => {
        const events: TimelineEventIF[] = [];

        if (!OEARecs) return events;

        // 處理門急資料
        OEARecs.OERecs.forEach((oeRec) => {
            // 組合content 從soapData 依照 日期、科別名稱、醫師名稱 找到對應的資料 抓診斷
            let contentData = '';
            if (soapDataList) {
                const soap: SoapDataIF | undefined = soapDataList.find((soap) => {
                    return soap.SeeDate.trim() === oeRec.chDate.trim() &&
                        soap.SecName.trim() === oeRec.chSecName.trim() &&
                        soap.DocName.trim() === oeRec.chDocName.trim();
                });
                if (soap) {
                    contentData += soap.Icd10No1 + ' ' + soap.Icd10Name1 + '\n';
                    contentData += soap.Icd10No2 + ' ' + soap.Icd10Name2 + '\n';
                    contentData += soap.Icd10No3 + ' ' + soap.Icd10Name3 + '\n';
                }
            }
            let opdEvent: TimelineEventIF = new Object();
            opdEvent.title = convertToTWDateHyphenFromTWDateString(oeRec.chDate) + ' ' + oeRec.chSecName;
            if (oeRec.chRoom === 'AAAA') {
                opdEvent.opdType = '強批';
            }
            if (oeRec.chRoom === 'LLLL') {
                opdEvent.opdType = '連處';
            }
            if (oeRec.chRoom === 'AAAA' || oeRec.chRoom === 'LLLL') {
                opdEvent.subTitle = oeRec.chSecName + ' ' + oeRec.chDocName + ' 醫師';
            } else {
                opdEvent.subTitle = '門急診 ' + oeRec.chDocName + ' 醫師';
            }
            opdEvent.content = contentData;
            opdEvent.icon = 'pi pi-users';
            opdEvent.color = '#FF9800';
            //日期時段診間序號
            opdEvent.tmId = 'OPD' + '_' + oeRec.chDate + '_' + oeRec.chPNC + '_' + oeRec.chRoom + '_' + oeRec.intRegNo;
            events.push(opdEvent);
        });

        // 處理住院資料 
        OEARecs.AdmRecs.forEach((admRec) => {
            // 比照門診做法，從 dischargeNoteList 取得出院診斷內容
            let contentData = '出院日:' + convertToTWDateHyphenFromTWDateString(admRec.chAd1DateC) + '\n';
            if (dischargeNoteList && dischargeNoteList.length > 0) {
                const note = dischargeNoteList.find(dn =>
                    (dn.DischargeDate && dn.DischargeDate.trim() === admRec.chAd1DateC.trim()) ||
                    (dn.AdmDate && dn.AdmDate.trim() === admRec.chAd1Date.trim())
                );
                if (note) {
                    const diagLines: string[] = [];
                    if (note.DisDiagCode1 || note.DisDiagName1) diagLines.push(`${note.DisDiagCode1 || ''} ${note.DisDiagName1 || ''}`.trim());
                    if (note.DisDiagCode2 || note.DisDiagName2) diagLines.push(`${note.DisDiagCode2 || ''} ${note.DisDiagName2 || ''}`.trim());
                    if (note.DisDiagCode3 || note.DisDiagName3) diagLines.push(`${note.DisDiagCode3 || ''} ${note.DisDiagName3 || ''}`.trim());
                    if (note.DisDiagCode4 || note.DisDiagName4) diagLines.push(`${note.DisDiagCode4 || ''} ${note.DisDiagName4 || ''}`.trim());
                    if (note.DisDiagCode5 || note.DisDiagName5) diagLines.push(`${note.DisDiagCode5 || ''} ${note.DisDiagName5 || ''}`.trim());
                    if (diagLines.length > 0) {
                        contentData += '\n' + diagLines.join('\n\n');
                    }
                }
            }
            events.push({
                title: convertToTWDateHyphenFromTWDateString(admRec.chAd1Date) + ' ' + admRec.chSecName,
                subTitle: '住院 ' + admRec.chDocName + ' 醫師',
                content: contentData,
                icon: 'pi pi-home',
                color: '#ff3b3b',
                tmId: 'ADM' + '_' + mrNo + '_' + admRec.chAd1Date + '_' + admRec.chAd1DateC
            });
        });

        // 依照title降冪排序
        events.sort((a, b) => {
            if (a.title && b.title) {
                return b.title.localeCompare(a.title);
            }
            return 0;
        });
        return events;
    }, [mrNo]);

    // 抓資料 - 使用 useCallback 優化，避免重複創建函數
    const getTimelineData = useCallback(async (): Promise<TimelineEventIF[]> => {
        try {
            // 處理參數
            if (!mrNo) return [];

            // 開抓 - 使用已經計算好的日期範圍
            const _OEARecs: OEARecsIF = await _mrDataService.GetOEARecs({ mrNo, sDate: startDate, eDate: endDate });
            const _SoapData: SoapDataIF[] = await _mrDataService.SoapData({ mrNo, sDate: startDate, eDate: endDate });
            const _DischargeNote: DischargeNoteIF[] = await _mrDataService.GetDischargeNote({ sDate: startDate, eDate: endDate, mrNo: mrNo });

            // 轉資料
            const eventData: TimelineEventIF[] = ConvertOEARecsToTimeline(_OEARecs, _SoapData, _DischargeNote);
            return eventData;
        } catch (error) {
            showError(error);
            return [];
        }
    }, [mrNo, startDate, endDate, _mrDataService, ConvertOEARecsToTimeline]);

    // 加載時間線數據 - 只在必要時執行
    useEffect(() => {
        if (initialized && startDate && endDate) {
            const loadTimelineData = async () => {
                setLoading(true);
                try {
                    const events = await getTimelineData();
                    setTimelineEvents(events);
                } catch (error) {
                    showError('取得時間線資料失敗');
                    console.error(error);
                } finally {
                    setLoading(false);
                }
            };

            loadTimelineData();
        }
    }, [initialized, startDate, endDate, getTimelineData]);

    // 一個內容
    const OEAContent = (tmItem: TimelineEventIF) => {
        // 時間線標題
        const renderTitle = (
            <>
                {tmItem.opdType && <span className='text-lg text-green-500'>{tmItem.opdType}<br /></span>}
                <span className='text-2xl'>{tmItem.title}</span>
            </>
        )
        return (
            <Card title={renderTitle} subTitle={tmItem?.subTitle} className='mx-3 min-w-80'
                pt={{
                    body: {
                        className: 'p-2'
                    },
                    content: {
                        className: 'pb-2 pt-0'
                    }
                }}
            >
                <p className="break-words">
                    {tmItem.content && tmItem.content.split('\n').map((line, index) => (
                        <React.Fragment key={index}>
                            {line}
                            <br />
                        </React.Fragment>
                    ))}
                </p>
                {/* 門診查看更多 */}
                {tmItem && tmItem.tmId && tmItem.tmId.toUpperCase().startsWith('OPD_') &&
                    <Button label="更多" className="m-0 p-4"
                        onClick={() => {
                            if (tmItem.tmId) {
                                if (tmItem.tmId.toUpperCase().startsWith('OPD_')) {
                                    const [type, seeDate, pnc, room, seqNo] = tmItem.tmId.split('_');
                                    setOpdDetail({ seeDate: seeDate, pnc: pnc, room: room, seqNo: parseInt(seqNo) });
                                }
                            }
                        }}
                    />
                }
                {/* 住院查看更多 */}
                {tmItem.tmId && tmItem.tmId.toUpperCase().startsWith('ADM_') &&
                    <Button label="更多" className="m-0  p-4"
                        onClick={() => {
                            if (tmItem.tmId) {
                                if (tmItem.tmId.toUpperCase().startsWith('ADM_')) {
                                    const [type, mrNo, admDate, admDateC] = tmItem.tmId.split('_');
                                    setAdmDetail({ admDate: admDate, admDisDate: admDateC, mrNo: mrNo });
                                }
                            }
                        }}
                    />
                }
            </Card>
        );
    };
    const OEAMarker = (item: TimelineEventIF) => {
        return (
            <span className="flex w-2rem h-2rem align-items-center justify-content-center text-white border-circle z-1 shadow-1" style={{ backgroundColor: item.color }}>
                <i className={item.icon}></i>
            </span>
        );
    };

    useEffect(() => {
        const timelineWrapper = timelineWrapperRef.current;
        const timeline = timelineRef.current;
        if (!timelineWrapper || !timeline) return;

        // 定義 wheel handler
        const handleWheel = (e: WheelEvent) => {
            const { scrollLeft, scrollWidth, clientWidth } = timeline;
            const atStart = scrollLeft === 0;
            const atEnd = scrollLeft + clientWidth >= scrollWidth - 1;
            if ((e.deltaY < 0 && !atStart) || (e.deltaY > 0 && !atEnd)) {
                e.preventDefault();
                timeline.scrollLeft += e.deltaY;
            }
        };

        // 綁定事件，passive: false
        timelineWrapper.addEventListener('wheel', handleWheel, { passive: false });

        // 清理
        return () => {
            timelineWrapper.removeEventListener('wheel', handleWheel);
        };
    }, [timelineEvents]);

    return (

        <>
            {/* 門診摘要 */}
            {opdDetail && (
                <Dialog
                    id='opdSummary'
                    header={`門診摘要-${opdDetail.seeDate}-${mrBasic?.Name}`}
                    onHide={() => {
                        setOpdDetail(null);
                    }}
                    visible={opdDetail !== null}
                    className='w-[90%]'
                    dismissableMask={true}
                >
                    <CaseOpdSoap
                        seeDate={opdDetail.seeDate}
                        pnc={opdDetail.pnc}
                        room={opdDetail.room}
                        seqNo={opdDetail.seqNo}
                    />

                </Dialog>
            )}
            {/* 住院摘要 */}
            {admDetail && (
                <Dialog
                    id='admSummary'
                    header={`住院摘要-${admDetail.admDate}-${mrBasic?.Name}`}
                    onHide={() => {
                        setAdmDetail(null);
                    }}
                    visible={admDetail !== null}
                    className='w-[90%]'
                    dismissableMask={true}
                    contentClassName=''
                >
                    <CaseAdmDetail
                        admDate={admDetail.admDate}
                        admDisDate={admDetail.admDisDate}
                        mrNo={admDetail.mrNo}
                    />
                </Dialog>
            )}


            {/* 門急住時間線 */}
            {/* onWheel 將滾輪轉成水平scroll */}
            {/* 如果timelineEvents 長度是0 無時間線紀錄或病歷號碼異常 */}
            {/* 如果timelineEvents 長度不是0 就顯示時間線 */}
            {loading && <p><ProgressSpinner />載入中...</p>}
            {!loading && timelineEvents.length === 0 && <p>無門急住紀錄</p>}
            {
                !loading && timelineEvents.length !== 0 &&
                <div
                    className="relative"
                    ref={timelineWrapperRef}

                >
                    <div
                        id="mrTimeline"
                        ref={timelineRef}
                        className="min-w-full overflow-x-auto overflow-y-hidden custom_scrollbar"
                    >
                        <Timeline value={timelineEvents} align="top" layout='horizontal' marker={OEAMarker} content={OEAContent}
                            pt={{
                                opposite: {
                                    className: 'hidden'
                                }
                            }}
                        />
                    </div>
                    <Button
                        icon="pi pi-arrow-left"
                        className="absolute bottom-2 left-2 roundd-full w-3rem h-3rem p-0"
                        onClick={() => {
                            const mrTimeline = document.getElementById('mrTimeline');
                            if (mrTimeline) {
                                mrTimeline.scrollLeft = 0;
                            }
                        }}
                        tooltip="滑到最前面"
                        tooltipOptions={{ position: 'top' }}
                        severity="secondary"
                    />
                    <Button
                        icon="pi pi-arrow-right"
                        className="absolute bottom-2 right-2 rounded-full w-3rem h-3rem p-0"
                        onClick={() => {
                            const mrTimeline = document.getElementById('mrTimeline');
                            if (mrTimeline) {
                                mrTimeline.scrollLeft = mrTimeline.scrollWidth;
                            }
                        }}
                        tooltip="滑到最後面"
                        tooltipOptions={{ position: 'top' }}
                        severity="secondary"
                    />
                </div>
            }
        </>

    );
};

export default OEATimeLine;
