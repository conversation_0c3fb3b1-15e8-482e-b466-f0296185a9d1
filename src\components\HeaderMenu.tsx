// import React, { useEffect, useState } from 'react';
// import { MegaMenu } from 'primereact/megamenu';
// import { MenuItem } from 'primereact/menuitem';
// import { ConfirmDialog } from 'primereact/confirmdialog';
// import { confirmDialog } from 'primereact/confirmdialog';
// import Cookies from 'universal-cookie';
// import imgLogout from '../assets/images/log-out-40.png';
// import { useNavigate } from "react-router-dom";
// import 'primereact/resources/primereact.min.css';
// import 'primeicons/primeicons.css';

// export default function HeaderMenu() {
//     // const [isLoggedIn, setIsLoggedIn] = useState(false);
//     // useEffect(() => {
//     //     const checkLoginStatus = () => {
//     //         const loggedIn = cookie.get(Constants.Cookies.UserID) !== undefined && cookie.get(Constants.Cookies.UserID) !== '' ? true : false;
//     //         setIsLoggedIn(loggedIn);
//     //         console.log('isLoggedIn:', loggedIn);

//     //     };

//     //     checkLoginStatus();
//     // }, []);
//     const navigate = useNavigate();
//     const cookie = new Cookies();
//     const items: MenuItem[] = [
//         {
//             label: '查詢個案', icon: 'pi pi-fw pi-video',
//             command: () => {
//                 navigate('/caselist');
//             }

//         }, {
//             label: '查詢', icon: 'pi pi-fw pi-video',
//             items: [
//                 [
//                     {
//                         label: 'Video 1',
//                         items: [{ label: 'Video 1.1' }, { label: 'Video 1.2' }]
//                     },
//                     {
//                         label: 'Video 2',
//                         items: [{ label: 'Video 2.1' }, { label: 'Video 2.2' }]
//                     }
//                 ],
//                 [
//                     {
//                         label: 'Video 3',
//                         items: [{ label: 'Video 3.1' }, { label: 'Video 3.2' }]
//                     },
//                     {
//                         label: 'Video 4',
//                         items: [{ label: 'Video 4.1' }, { label: 'Video 4.2' }]
//                     }
//                 ]
//             ]
//         }
//         // ,
//         // {
//         //     label: 'Users', icon: 'pi pi-fw pi-users',
//         //     items: [
//         //         [
//         //             {
//         //                 label: 'User 1',
//         //                 items: [{ label: 'User 1.1' }, { label: 'User 1.2' }]
//         //             },
//         //             {
//         //                 label: 'User 2',
//         //                 items: [{ label: 'User 2.1' }, { label: 'User 2.2' }]
//         //             },
//         //         ],
//         //         [
//         //             {
//         //                 label: 'User 3',
//         //                 items: [{ label: 'User 3.1' }, { label: 'User 3.2' }]
//         //             },
//         //             {
//         //                 label: 'User 4',
//         //                 items: [{ label: 'User 4.1' }, { label: 'User 4.2' }]
//         //             }
//         //         ],
//         //         [
//         //             {
//         //                 label: 'User 5',
//         //                 items: [{ label: 'User 5.1' }, { label: 'User 5.2' }]
//         //             },
//         //             {
//         //                 label: 'User 6',
//         //                 items: [{ label: 'User 6.1' }, { label: 'User 6.2' }]
//         //             }
//         //         ]
//         //     ]
//         // },
//         // {
//         //     label: 'Events', icon: 'pi pi-fw pi-calendar',
//         //     items: [
//         //         [
//         //             {
//         //                 label: 'Event 1',
//         //                 items: [{ label: 'Event 1.1' }, { label: 'Event 1.2' }]
//         //             },
//         //             {
//         //                 label: 'Event 2',
//         //                 items: [{ label: 'Event 2.1' }, { label: 'Event 2.2' }]
//         //             }
//         //         ],
//         //         [
//         //             {
//         //                 label: 'Event 3',
//         //                 items: [{ label: 'Event 3.1' }, { label: 'Event 3.2' }]
//         //             },
//         //             {
//         //                 label: 'Event 4',
//         //                 items: [{ label: 'Event 4.1' }, { label: 'Event 4.2' }]
//         //             }
//         //         ]
//         //     ]
//         // },
//         // {
//         //     label: 'Settings', icon: 'pi pi-fw pi-cog',
//         //     items: [
//         //         [
//         //             {
//         //                 label: 'Setting 1',
//         //                 items: [{ label: 'Setting 1.1' }, { label: 'Setting 1.2' }]
//         //             },
//         //             {
//         //                 label: 'Setting 2',
//         //                 items: [{ label: 'Setting 2.1' }, { label: 'Setting 2.2' }]
//         //             },
//         //             {
//         //                 label: 'Setting 3',
//         //                 items: [{ label: 'Setting 3.1' }, { label: 'Setting 3.2' }]
//         //             }
//         //         ],
//         //         [
//         //             {
//         //                 label: 'Technology 4',
//         //                 items: [{ label: 'Setting 4.1' }, { label: 'Setting 4.2' }]
//         //             }
//         //         ]
//         //     ]
//         // }

//     ];

//     const returnToLogin = () => {
//         confirmDialog({
//             message: '你確定要登出嗎?',
//             header: '登出確認',
//             icon: 'pi pi-exclamation-triangle',
//             acceptClassName: 'p-button-danger',
//             rejectClassName: 'p-button-secondary',
//             acceptLabel: '確定',
//             rejectLabel: '取消',
//             accept: () => {
//                 for (let key in cookie.getAll()) {
//                     cookie.remove(key);

//                 }
//                 // setIsLoggedIn(false);
//                 navigate('/logout');
//                 // window.location.href = '/login';
//                 // Navigate({ to: '/' });
//             },
//             reject: () => {

//             }
//         });
//     }
//     const usingUser = '使用者： ' + cookie.get('UserName');
//     // 建立一個圖片
//     // let logoutButton = <img src={imgLogout} alt="" height="28" width="28" style={{ border: 'none' }} onClick={returnToLogin} />
//     let logoutButton = (
//         <a href="#" onClick={returnToLogin}>
//             <img src={imgLogout.src} alt="" height="28" width="28" style={{ border: 'none' }} />
//         </a>
//     );
//     // logoutButton = <Button icon="pi pi-times" rounded severity="danger" aria-label="Cancel" onClick={returnToLogin} />
//     //log-out-40.png

//     let end = usingUser && (
//         <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'nowrap' }}>
//             <span><b>{usingUser}</b></span><p>　</p>{logoutButton}
//         </div>
//     );




//     return (
//         cookie.get(Cookies.UserID) !== undefined && cookie.get(Cookies.UserID) !== ''
//             ?
//             (<div className="card " style={{ position: 'sticky', top: 0, zIndex: 999 }} >
//                 <ConfirmDialog />
//                 {/* <MegaMenu model={items} orientation="horizontal" start={start} end={end} breakpoint="960px" className="p-3 surface-0 shadow-2" style={{ borderRadius: '3rem' }} /> */}
//                 <MegaMenu model={items} orientation="horizontal" breakpoint="960px" end={end} className="p-3 surface-0 shadow-2 bg-indigo-100" style={{ borderRadius: '1rem', height: "50px" }} />
//             </div>) : <></>

//     )
// }
