'use client';
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Button } from 'primereact/button';
import { Avatar } from 'primereact/avatar';
import { Card } from 'primereact/card';
import { Badge } from 'primereact/badge';
import { Divider } from 'primereact/divider';
import { Sidebar } from 'primereact/sidebar';
import { TabItemIF } from './Tab/tabIF';
import { AutoComplete } from "primereact/autocomplete";
import { AuthService } from '@/services/AuthService';
import Cookies from 'universal-cookie';
import { Menu } from 'primereact/menu';
import CaseSearch from '@/components/CaseSearch';
import BlankFrame from './Tab/BlankFrame';
import { CnacerIcon } from './icons';
import { getIdPossibleSuffixes } from '@/utils/AutoComplete';
import { Toast } from 'primereact/toast';
import { setToastRef, showError, showInfo } from '@/components/Toast';

import SidebarToggle from './Icons/SidebarToggle';
import { Checkbox } from 'primereact/checkbox';
import { useIsMobile } from '@/hooks/useIsMobile';
import DeviceInfo from './DeviceInfo';

// 定義選單項目的類型接口
interface MenuItemIF {
    label?: string;
    icon?: string;
    badge?: number;
    shortcut?: string;
    separator?: boolean;
    command?: () => void;
    template?: (item: any) => React.ReactNode;
    items?: MenuItemIF[];
}


interface LeftBarProps {
    tabPageRef: React.MutableRefObject<{
        addTab: (content: TabItemIF) => void;
        removeTab: (title: string) => void;
        clearTabs: () => void;
        tabs: TabItemIF[];
        setTabs: (tabs: TabItemIF[]) => void;
        setIsShowTabHeader: (isShow: boolean) => void;
        isShowTabHeader: boolean;
    } | null>;
    navigationItems?: Array<{
        id: string;
        icon: string;
        label: string;
        color: string;
        badge?: number;
        component: React.ReactNode;
    }>;
    isMobile?: boolean;
    className?: string;
    onSidebarExpandChange?: (expanded: boolean) => void;
}

interface UserBasicIF {
    userName: string;
    userImage?: string;
}

interface SearchPatient {
    name: string;
    code: string;
}

const LeftBar: React.FC<LeftBarProps> = ({
    tabPageRef,
    navigationItems = [],
    isMobile: isMobileProp,
    className,
    onSidebarExpandChange
}) => {
    // 使用自定義 Hook 檢測行動裝置，如果有傳入 prop 則優先使用
    const autoDetectedIsMobile = useIsMobile(768); // 768px 為 Tailwind md 斷點
    const isMobile = isMobileProp !== undefined ? isMobileProp : autoDetectedIsMobile;

    const [sidebarExpanded, setSidebarExpanded] = useState(false);
    const sidebarExpandeWidth = 'w-70';
    const sidebarCollapseWidth = 'w-10';
    const [openTabs, setOpenTabs] = useState<TabItemIF[]>([]);
    const [hideTabHeader, setHideTabHeader] = useState(false);
    const userMenuRef = useRef<Menu>(null);

    // 展開狀態管理 - 使用選單項目的 label 作為 key
    const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});

    // 切換展開狀態
    const toggleExpanded = (itemKey: string) => {
        setExpandedItems(prev => ({
            ...prev,
            [itemKey]: !prev[itemKey]
        }));
    };

    // 從 NavBar 導入的狀態和函數
    const authService = useMemo(() => new AuthService(), []);
    const cookie = useMemo(() => new Cookies(), []);
    const [userBasic, setUserBasic] = useState<UserBasicIF>();
    const [searchItems, setSearchItems] = useState<SearchPatient[]>([]);
    const [searchItem, setSearchItem] = useState<string>();
    const menuRef = useRef<Menu>(null);

    // 用戶認證
    useEffect(() => {
        const checkLoginAndSetUser = async () => {
            // 檢查登入狀態
            await authService.CheckLogin();
            // 賦值
            const userBasicData = cookie.get('USER_BASIC');
            setUserBasic(userBasicData);

            console.log(userBasicData);
            if (!userBasicData?.userName) {
                showError('請先登入');
                window.location.replace("/Login");
            }
        };

        checkLoginAndSetUser();
    }, [authService, cookie]);

    // 新增一個 useEffect 來監聽 tabPageRef.current?.tabs 的變化
    useEffect(() => {
        // 定期檢查標籤頁的變化，但只在標籤頁真的有變化時才更新
        const intervalId = setInterval(() => {
            if (tabPageRef.current) {
                const currentTabs = tabPageRef.current.tabs;
                setOpenTabs(prevTabs => {
                    // 只有在標籤頁數量或內容真的改變時才更新
                    if (prevTabs.length !== currentTabs.length ||
                        !prevTabs.every((tab, index) => tab.tabID === currentTabs[index]?.tabID)) {
                        return [...currentTabs];
                    }
                    return prevTabs;
                });
            }
        }, 300);

        return () => clearInterval(intervalId);

    }, []);

    // 用戶登出選單
    const userMenuItems = [
        {
            label: '設定',
            items: [
                {
                    label: '隱藏分頁標籤',
                    icon: 'pi pi-eye',
                    template: (item: any) => (
                        <div className="flex align-items-center p-2">
                            <Checkbox
                                id={item.label}
                                checked={hideTabHeader}
                                onChange={(e) => {
                                    const isChecked = e.checked ?? false;
                                    setHideTabHeader(isChecked);
                                    if (tabPageRef.current) {
                                        tabPageRef.current.setIsShowTabHeader(!isChecked);
                                    }
                                }}
                            />
                            <label
                                className="ml-2 cursor-pointer select-none"
                                htmlFor={item.label}
                                onClick={(e) => {
                                    e.preventDefault();
                                    const newValue = !hideTabHeader;
                                    setHideTabHeader(newValue);
                                    if (tabPageRef.current) {
                                        tabPageRef.current.setIsShowTabHeader(!newValue);
                                    }
                                }}
                            >
                                {item.label}
                            </label>
                        </div>
                    )
                }
            ]
        },
        {
            separator: true
        },
        {
            label: '登出',
            icon: 'pi pi-sign-out',
            command: () => {
                cookie.remove('USER_BASIC');
                cookie.remove('SESSION_TOKEN');
                // 設置一個標記，告訴 beforeunload 監聽器不要攔截登出
                window.sessionStorage.setItem('isLoggingOut', 'true');
                window.location.href = "/Login";
            }
        }
    ];

    // 搜尋功能
    const search = (event: { query?: string }) => {
        // 自動填補身分證字號
        const idList = event.query ? getIdPossibleSuffixes(event.query.toUpperCase()) : [];
        setSearchItems(idList.map((idValue) => ({ name: idValue, code: idValue })));
    }

    // 處理搜尋
    const handleSearchPatient = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' && searchItem) {
            console.log('searchItem:', searchItem);
            // 搜尋個案
            const tab: TabItemIF = {
                title: '個案查詢',
                tabID: '個案查詢',
                content: <CaseSearch key={Date.now()} tabPageRef={tabPageRef} mrNoOrName={searchItem} />,
            };
            addTab(tab);
        }
    }

    // 開啟新分頁
    const addTab = (tab: TabItemIF) => {
        if (tabPageRef.current) {
            tabPageRef.current.addTab(tab);
        } else {
            showError('Tab page reference is not available');
        }
    };



    // 渲染菜單項目
    const itemRenderer = (item: any) => (
        <a className="flex align-items-center p-menuitem-link">
            <span className={item.icon} />
            <span className="mx-2">{item.label}</span>
            {item.badge && <Badge className="ml-auto" value={item.badge} />}
            {item.shortcut && <span className="ml-auto border-1 surface-border border-round surface-100 text-xs p-1">{item.shortcut}</span>}
        </a>
    );

    // 遞迴渲染選單項目組件
    const MenuItemRenderer: React.FC<{
        item: MenuItemIF;
        level: number;
        parentKey?: string;
        isCollapsed?: boolean;
    }> = ({ item, level, parentKey = '', isCollapsed = false }) => {
        if (item.separator) {
            return <Divider className="my-2" />;
        }

        const itemKey = parentKey ? `${parentKey}-${item.label || 'item'}` : (item.label || 'item');
        const hasChildren = item.items && item.items.length > 0;
        const isExpanded = expandedItems[itemKey];
        const indentClass = level > 0 ? `ml-${Math.min(level * 4, 16)}` : '';

        const handleItemClick = () => {
            if (hasChildren) {
                toggleExpanded(itemKey);
            } else if (item.command) {
                item.command();
                // 關閉側邊欄（手機版）
                if (isMobile) {
                    setSidebarExpanded(false);
                }
            }
        };

        // 如果是收折狀態且不是第一層，不顯示
        if (isCollapsed && level > 0) {
            return null;
        }

        return (
            <div key={itemKey}>
                {/* 選單項目按鈕 */}
                <div className={`${indentClass}`}>
                    <div
                        className={`w-full cursor-pointer transition-colors duration-200 rounded-md ${isCollapsed
                            ? 'text-white hover:bg-gray-800 p-2'
                            : 'text-white hover:bg-gray-700 p-3'
                            }`}
                        onClick={handleItemClick}
                        title={isCollapsed && !isMobile ? item.label : ''}
                    >
                        <div className="flex items-center justify-between w-full">
                            <div className="flex items-center">
                                {item.icon && (
                                    <span className={`pi ${item.icon} ${!isCollapsed ? 'mr-3' : ''} ${isMobile ? 'text-lg' : 'text-base'}`} />
                                )}
                                {!isCollapsed && (
                                    <span className={`${isMobile ? 'text-lg' : 'text-base'} font-medium`}>{item.label}</span>
                                )}
                            </div>
                            {!isCollapsed && (
                                <div className="flex items-center">
                                    {item.badge && (
                                        <Badge className="ml-2" value={item.badge} />
                                    )}
                                    {item.shortcut && (
                                        <span className="ml-2 border border-gray-600 rounded text-xs px-1 py-0.5 bg-gray-800 text-gray-300">
                                            {item.shortcut}
                                        </span>
                                    )}
                                    {hasChildren && (
                                        <span className={`pi ${isExpanded ? 'pi-chevron-down' : 'pi-chevron-right'} ml-2 ${isMobile ? 'text-base' : 'text-sm'}`} />
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* 子選單項目 */}
                {hasChildren && isExpanded && !isCollapsed && (
                    <div className="ml-4 mt-1">
                        {item.items!.map((subItem, index) => (
                            <MenuItemRenderer
                                key={`${itemKey}-${index}`}
                                item={subItem}
                                level={level + 1}
                                parentKey={itemKey}
                                isCollapsed={false}
                            />
                        ))}
                    </div>
                )}
            </div>
        );
    };

    // NavBar 的選單項目
    const menuItems: MenuItemIF[] = [
        {
            label: '個案查詢',
            icon: 'pi-search',
            command: () => {
                const tab: TabItemIF = {
                    title: '個案查詢',
                    tabID: '個案查詢',
                    content: <CaseSearch tabPageRef={tabPageRef} />,
                };
                addTab(tab);
            }
        },
        {
            label: '新增頁面',
            icon: 'pi-star',
            command: () => {
                const randomNumber = Math.floor(Math.random() * 1000000);
                const tab: TabItemIF = {
                    title: `新增頁面${randomNumber}`,
                    tabID: `新增頁面${randomNumber}`,
                    content: <BlankFrame />
                };
                addTab(tab);
            }
        },
        {
            label: '裝置檢測',
            icon: 'pi-mobile',
            command: () => {
                const tab: TabItemIF = {
                    title: '裝置檢測',
                    tabID: '裝置檢測',
                    content: <DeviceInfo />
                };
                addTab(tab);
            }
        },
        {
            label: 'Projects',
            icon: 'pi-folder',
            items: [
                {
                    label: 'Core',
                    icon: 'pi-bolt',
                    shortcut: '⌘+S',
                    command: () => {
                        const tab: TabItemIF = {
                            title: 'Core',
                            tabID: 'Core',
                            content: <BlankFrame />
                        };
                        addTab(tab);
                    }
                },
                {
                    label: 'Blocks',
                    icon: 'pi-server',
                    shortcut: '⌘+B',
                    command: () => {
                        const tab: TabItemIF = {
                            title: 'Blocks',
                            tabID: 'Blocks',
                            content: <BlankFrame />
                        };
                        addTab(tab);
                    }
                },
                {
                    label: 'UI Kit',
                    icon: 'pi-pencil',
                    shortcut: '⌘+U',
                    command: () => {
                        const tab: TabItemIF = {
                            title: 'UI Kit',
                            tabID: 'UI Kit',
                            content: <BlankFrame />
                        };
                        addTab(tab);
                    }
                },
                {
                    separator: true
                },
                {
                    label: 'Templates',
                    icon: 'pi-palette',
                    items: [
                        {
                            label: 'Apollo',
                            icon: 'pi-palette',
                            badge: 2,
                            command: () => {
                                const tab: TabItemIF = {
                                    title: 'Apollo',
                                    tabID: 'Apollo',
                                    content: <BlankFrame />
                                };
                                addTab(tab);
                            }
                        },
                        {
                            label: 'Ultima',
                            icon: 'pi-palette',
                            badge: 3,
                            command: () => {
                                const tab: TabItemIF = {
                                    title: 'Ultima',
                                    tabID: 'Ultima',
                                    content: <BlankFrame />
                                };
                                addTab(tab);
                            }
                        },
                        {
                            label: 'Contact',
                            icon: 'pi-envelope',
                            badge: 5,
                            items: [
                                {
                                    label: 'Email Support',
                                    icon: 'pi-envelope',
                                    command: () => {
                                        const tab: TabItemIF = {
                                            title: 'Email Support',
                                            tabID: 'Email Support',
                                            content: <BlankFrame />
                                        };
                                        addTab(tab);
                                    }
                                },
                                {
                                    label: 'Live Chat',
                                    icon: 'pi-comments',
                                    command: () => {
                                        const tab: TabItemIF = {
                                            title: 'Live Chat',
                                            tabID: 'Live Chat',
                                            content: <BlankFrame />
                                        };
                                        addTab(tab);
                                    }
                                },
                                {
                                    label: 'Phone Support',
                                    icon: 'pi-phone',
                                    items: [
                                        {
                                            label: 'Technical Support',
                                            icon: 'pi-cog',
                                            command: () => {
                                                const tab: TabItemIF = {
                                                    title: 'Technical Support',
                                                    tabID: 'Technical Support',
                                                    content: <BlankFrame />
                                                };
                                                addTab(tab);
                                            }
                                        },
                                        {
                                            label: 'Sales Support',
                                            icon: 'pi-dollar',
                                            command: () => {
                                                const tab: TabItemIF = {
                                                    title: 'Sales Support',
                                                    tabID: 'Sales Support',
                                                    content: <BlankFrame />
                                                };
                                                addTab(tab);
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ];

    // 側邊欄渲染函數
    const renderSidebar = () => {
        const desktopClasses = `min-h-screen bg-gray-600 transition-all duration-300 shrink-0 z-40 ${sidebarExpanded ? sidebarExpandeWidth : sidebarCollapseWidth} flex flex-col h-full`;

        const sidebarContent = (
            <div className={desktopClasses}>
                {/* Logo 與 Header */}
                <div className={`${'border-b border-gray-700 w-full'}`}>
                    <div className="flex items-center justify-between w-full">
                        {(sidebarExpanded) && (
                            <Button
                                className="p-button-text p-0 mr-4"
                                onClick={() => {
                                    const tab: TabItemIF = {
                                        title: 'Dashboard',
                                        tabID: 'Dashboard',
                                    };
                                    addTab(tab);
                                    if (isMobile) setSidebarExpanded(false);
                                }}
                            >
                                <div className="flex items-center space-x-2">
                                    <CnacerIcon className="h-6 w-6" />
                                    <span className="text-lg font-bold text-white">CancerBook</span>
                                </div>
                            </Button>
                        )}
                        {true && (
                            <Button
                                icon={
                                    <SidebarToggle
                                        className="icon-xl-heavy"
                                        color="white"
                                    />
                                }
                                className="p-button-text p-button-rounded p-button-secondary"
                                onClick={() => {
                                    setSidebarExpanded(!sidebarExpanded);
                                    console.log('sidebarExpanded:', sidebarExpanded);
                                }}
                            />
                        )}
                    </div>
                </div>


                {/* 搜尋功能 */}
                {(sidebarExpanded) && (
                    <div className='px-4 border-b border-gray-700'>
                        <AutoComplete
                            field="name"
                            value={searchItem}
                            suggestions={searchItems}
                            completeMethod={search}
                            onChange={(e) => {
                                // 檢查 e.value 的類型，如果是 SearchPatient 物件則取 name 屬性
                                if (typeof e.value === 'string') {
                                    setSearchItem(e.value);
                                } else if (e.value && typeof e.value === 'object' && 'code' in e.value) {
                                    setSearchItem(e.value.code);
                                } else {
                                    setSearchItem('');
                                }
                                console.log('searchItem updated:', searchItem);
                            }}
                            onKeyDown={(e) => handleSearchPatient(e as React.KeyboardEvent<HTMLInputElement>)}
                            type="search"
                            placeholder="快速搜尋"
                            className="w-full"
                            inputClassName="w-full"
                            panelClassName="z-50"
                        />
                    </div>
                )}

                {/* 收折時的小圖案*/}

                <div className="p-1 space-y-2">
                    {/* NavBar 的選單項目 */}
                    {!sidebarExpanded && menuItems.map((item, index) => (
                        <MenuItemRenderer
                            key={`menu-collapsed-${index}`}
                            item={item}
                            level={0}
                            isCollapsed={true}
                        />
                    ))}
                </div>


                {/* 展開時的內容 */}
                {(sidebarExpanded) && (
                    <div className='p-4 flex-1 overflow-y-auto custom_scrollbar'>
                        <div className="space-y-4">
                            {/* NavBar 選單項目（展開狀態） */}
                            <div className="space-y-1">
                                {menuItems.map((item, index) => (
                                    <MenuItemRenderer
                                        key={`menu-expanded-${index}`}
                                        item={item}
                                        level={0}
                                        isCollapsed={false}
                                    />
                                ))}
                            </div>

                            <Divider />

                            <div>
                                {/* 標籤列表 */}
                                <div className="mb-4">
                                    <h3 className={`text-white text-base font-medium mb-3`}>分頁</h3>
                                    <div className="space-y-2">
                                        {openTabs.map((tab) => (
                                            <div key={tab.tabID}
                                                className="px-3 py-2 bg-gray-800 border-gray-700 cursor-pointer hover:bg-gray-700 rounded-md"
                                                onClick={() => {
                                                    if (tabPageRef.current) {
                                                        const existingTab = tabPageRef.current.tabs.find(t => t.tabID === tab.tabID);
                                                        if (existingTab) {
                                                            tabPageRef.current.addTab(existingTab);
                                                        }
                                                        if (isMobile) {
                                                            setSidebarExpanded(false);
                                                        }
                                                    }
                                                }}
                                            >
                                                <div className="flex justify-between items-center">
                                                    <div className="flex-1">
                                                        <div className={`text-white ${isMobile ? 'text-base' : 'text-sm'} font-medium truncate`}>
                                                            {tab.title}
                                                        </div>
                                                    </div>
                                                    <Button
                                                        icon="pi pi-times"
                                                        className="p-button-text p-button-secondary py-2 w-8"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            if (tabPageRef.current) {
                                                                tabPageRef.current.removeTab(tab.tabID);
                                                                setOpenTabs(openTabs.filter(t => t.tabID !== tab.tabID));
                                                            }
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* 用戶資料 - 底部 */}
                {(sidebarExpanded) && (
                    <div className="p-4 border-t border-gray-700 flex-shrink-0 bg-gray-700 rounded-full hover:cursor-pointer"
                        onClick={(e) => {
                            userMenuRef.current?.toggle(e);
                        }}
                    >
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <Avatar
                                    image={userBasic?.userImage}
                                    icon={!userBasic?.userImage ? "pi pi-user" : undefined}
                                    className="bg-blue-500"
                                    shape="circle"
                                    size={isMobile ? "large" : "normal"}
                                />
                                <div
                                    className="text-white cursor-pointer"
                                    onClick={(e) => userMenuRef.current?.toggle(e)}
                                >
                                    <div className={`${isMobile ? 'text-lg' : 'text-base'} font-semibold`}>{userBasic?.userName || '未登入'}</div>
                                </div>
                            </div>
                        </div>
                        <Menu model={userMenuItems} popup ref={userMenuRef} />
                    </div>
                )}
            </div>
        );



        return sidebarContent;
    };

    useEffect(() => {
        if (onSidebarExpandChange) onSidebarExpandChange(sidebarExpanded);
    }, [sidebarExpanded, onSidebarExpandChange]);



    return (
        <>
            <Toast ref={(ref) => setToastRef(ref)} />
            {/* 側邊欄 */}
            {renderSidebar()}
        </>
    );
};

export default LeftBar;
