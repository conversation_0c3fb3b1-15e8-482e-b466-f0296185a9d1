@tailwind base;
@tailwind components;
@tailwind utilities;



/* EUDC custom glyphs (Private Use Area) */

@font-face {
  font-family: 'EUDC-Tzuchi';            /* 你自訂的名字 */
  src: url('/fonts/EUDC-Tzuchi.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  unicode-range: U+E000-F8FF;
}

.eudc {
  font-family: 'EUDC-Tzuchi','Microsoft JhengHei', 'PingFang TC', 'Helvetica Neue', Arial, sans-serif;
}


:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}


body {
  color: rgb(var(--foreground-rgb));
  font-family: 'Microsoft JhengHei', 'PingFang TC', 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
    margin: 0;
    padding: 0;
    height: 100%;
    overflow: hidden;
  /* 在 globals.css 或其他全域 CSS 檔案中 */
  /* https://www.cursors-4u.com/cursor/2010/03/20/cool-outer-glow-pointer-set.html */
  cursor: url('/cur116.cur'), auto;
}
.flex-items {
    align-items: center;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: flex-start;
    white-space: nowrap;
}


@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* 客製化scrollbar */
.custom_scrollbar{
    &::-webkit-scrollbar{
      width: 6px;
      height: 6px;
    }
    &::-webkit-scrollbar-track {
      background-color: #e3f5ff;
      border-radius: 10px;
      margin: 20px 130px;
    }
    &::-webkit-scrollbar-thumb{
      border-radius: 10px;
      background-color: #7ccffc;
    }
}

/* CaseAdmDetail 專用樣式 */
.case-adm-detail {
  /* 改善文字可讀性 */
  .p-inputtextarea {
    line-height: 1.6;
    font-family: 'EUDC', 'Microsoft JhengHei', 'PingFang TC', 'Helvetica Neue', Arial, sans-serif;
  }

  /* 改善表格樣式 */
  .p-datatable {
    .p-datatable-thead > tr > th {
      background-color: #f8fafc;
      border-bottom: 2px solid #e2e8f0;
      font-weight: 600;
      color: #374151;
    }

    .p-datatable-tbody > tr > td {
      border-bottom: 1px solid #f1f5f9;
      padding: 0.5rem;
    }

    .p-datatable-tbody > tr:hover {
      background-color: #f8fafc;
    }
  }

  /* 改善卡片樣式 */
  .p-card {
    transition: all 0.2s ease-in-out;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-1px);
    }

    .p-card-body {
      padding: 1rem;
    }

    .p-card-content {
      padding: 0;
    }
  }

  /* 響應式字體大小和觸控優化 */
  @media (max-width: 768px) {
    .p-inputtextarea {
      font-size: 16px; /* 避免 iOS Safari 縮放 */
      padding: 12px;
      min-height: 120px;
    }

    .p-datatable {
      font-size: 14px;
    }

    .p-button {
      min-height: 44px; /* 觸控友善的最小尺寸 */
      min-width: 44px;
      padding: 8px 16px;
    }

    .p-card {
      margin-bottom: 16px;
    }

    /* 改善表格在小螢幕的顯示 */
    .p-datatable-wrapper {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }

    .p-datatable .p-datatable-thead > tr > th,
    .p-datatable .p-datatable-tbody > tr > td {
      padding: 8px 4px;
      font-size: 13px;
      white-space: nowrap;
    }
  }

  @media (min-width: 768px) and (max-width: 1023px) {
    .p-inputtextarea {
      font-size: 15px;
      padding: 10px;
    }

    .p-datatable {
      font-size: 14px;
    }
  }

  @media (min-width: 1024px) {
    .p-inputtextarea {
      font-size: 15px;
      padding: 12px;
    }

    .p-datatable {
      font-size: 14px;
    }
  }

  /* 改善滑動體驗 */
  .case-adm-detail {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 確保在所有裝置上都有良好的對比度 */
  .case-adm-detail .p-inputtextarea {
    background-color: #ffffff;
    border: 1px solid #d1d5db;
    color: #374151;
  }

  .case-adm-detail .p-inputtextarea:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
}


@media screen and (max-width: 960px) {
  .p-menubar .p-menubar-button {
    display: flex !important;
  }

  .p-menubar .p-menubar-root-list {
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    z-index: 1000;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 0 0 6px 6px;
    padding: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
}

/* 優化選單外觀 */
.p-menubar {
  padding: 0.5rem 1rem !important;
}

.p-menubar .p-menuitem-link {
  padding: 0.75rem 1rem !important;
}

.p-menubar .p-submenu-list {
  z-index: 1000 !important;
}
