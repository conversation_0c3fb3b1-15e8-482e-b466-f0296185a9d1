import axios, { AxiosRequestConfig } from 'axios';
import Cookies from 'universal-cookie';
import { SESSION_TOKEN } from '@/configs/constant';
/*
npm install axios

*/

//從storege取得token
// const authBearerToken = localStorage.getItem('token');
// 從COOKIE取得token
const cookie = new Cookies();

// POST方法
export async function apiPost(apiPath: string, requestBody?: any) {
    let authBearerToken = cookie.get(SESSION_TOKEN);
    const headers: { [key: string]: string } = {
        'Content-Type': 'application/json',
    };
    if (authBearerToken) {
        headers.Authorization = `Bearer ${authBearerToken}`;
    }
    try {
        const resp = await axios.post(apiPath, requestBody, {
            headers: headers,
            withCredentials: true,
        });
        return resp;
    } catch (error: any) {
        if (error.response && error.response.status.toString().startsWith('4')) {
            if (error.response.status === 401) {
                console.log('認證失效，請重新登入');
            } else {
                console.log('客戶端錯誤：', error.response.status);
            }
            throw error;
        }
        console.log('Service：', apiPath, error);
        throw error;
    }
}
// export async function apiPost(apiPath: string, requestBody?: any) {
//     let authBearerToken = cookie.get(SESSION_TOKEN);
//     const headers = {
//         'Content-Type': 'application/json',
//         Authorization: authBearerToken ? `Bearer ${authBearerToken}` : '',
//     };
//     if (requestBody === undefined) {
//         requestBody = {};
//     }

//     try {
//         const response = await axios.post(apiPath, requestBody, {
//             headers: headers,
//             withCredentials: true,
//         });

//         return response;
//     } catch (error: any) {
//         if (error.response.status.toString().startsWith('4')) {
//             if (error.response.status === 401) {
//                 console.log('認證失效，請重新登入');
//                 throw error;
//             }
//         }
//         console.log('Service：', apiPath, error);
//         throw error;
//     }
// }

// GET方法
export async function apiGet(apiPath: string, params?: any) {
    let authBearerToken = cookie.get(SESSION_TOKEN);
    const config: AxiosRequestConfig = {
        params,
        headers: {
            Authorization: authBearerToken ? `Bearer ${authBearerToken}` : undefined,
        },
        withCredentials: true,
    };

    try {
        const response = await axios.get(apiPath, config);
        if (!response.data) {
            console.log('Service：apiGet()' + apiPath, response);
        }

        return response;
    } catch (error: any) {
        if (error.response &&
            error.response.status &&
            error.response.status.toString().startsWith('4')) {
            if (error.response.status === 401) {
                console.log('認證失效，請重新登入');
                throw error;
            }
        }
        console.error('Error:', error);
        throw error;
    }
}
