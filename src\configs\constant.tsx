

// const apiEndpoint  = 'http://localhost:34981/psymanage';
// const apiEndpoint  = 'http://localhost:5015';
const httpsEnabled = 'N';
// const domain = 'localhost';
const domain = '************';
const port = 5015;

// 改用proxy  在setupProxy.js 設定
const getEndpoint = () => {
    // return "/api";

    let apiEndpoint = '';
    if (httpsEnabled.toString() === 'Y') {
        apiEndpoint = "https://" + domain + ":" + port + "/api";
    } else {
        apiEndpoint = "http://" + domain + ":" + port + "/api";
    }
    return apiEndpoint;
}

export const Cookies = {
    UserID: 'UserID',
    UserName: 'UserName',
    IsAdmin: 'IsAdmin',
    IsMember: 'IsMember',
};

export const ENDPOINT = getEndpoint();
export const USER_BASIC = 'USER_BASIC';
export const SESSION_TOKEN = 'SESSION_TOKEN';
export const AI_ENDPOINT = 'http://localhost:11434/v1';
