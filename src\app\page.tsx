'use client';
import { useRef, useEffect, useState } from "react";
import NavBar from "@/components/NavBar";
import LeftBar from "@/components/LeftBar";
import { TabPanel, TabView } from "primereact/tabview";
import { TabItemIF } from "@/components/Tab/tabIF";
import AppTabPage from "@/components/Tab/AppTabPage";
import { Button } from "primereact/button";
import { TabPageProvider } from "@/components/Tab/TabPageContext";
import { addLocale, locale } from "primereact/api";



export default function Home() {
  const tabPageRef = useRef<{
    addTab: (item: TabItemIF) => void;
    removeTab: (tabID: string) => void;
    clearTabs: () => void
    tabs: TabItemIF[];
    setTabs: (tabs: TabItemIF[]) => void;
    setIsShowTabHeader: (isShow: boolean) => void;
    isShowTabHeader: boolean;
  }>(null);

  locale('zh-TW');

  addLocale('zh-TW', {
    firstDayOfWeek: 1,
    dayNamesMin: ['日', '一', '二', '三', '四', '五', '六'],
    monthNames: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    monthNamesShort: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    today: '今天',
    clear: '清除',
    now: '現在',

  });

  return (
    <TabPageProvider>
      <div className="flex flex-row h-screen w-screen overflow-hidden">
        <LeftBar tabPageRef={tabPageRef} />
        {/* <div>
          <NavBar tabPageRef={tabPageRef} />
        </div> */}
        <div className="flex-1  overflow-auto">
          <AppTabPage ref={tabPageRef} />
        </div>
      </div >
    </TabPageProvider>
  );
}