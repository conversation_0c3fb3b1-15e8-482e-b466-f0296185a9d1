'use client';
import React, { useEffect } from 'react';
import { useState } from 'react';
import { ToggleButton } from 'primereact/togglebutton';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { AuthService } from '@/services/AuthService';
import Cookies from 'universal-cookie';
import { LoginReqIF } from '@/models/Login/LoginReqIF';
import { LoginRespIF } from '@/models/Login/LoginRespIF';
import { SESSION_TOKEN, USER_BASIC } from '@/configs/constant';
import AesEncoder from '@/modules/AesEncoder';
import Spinner from '@/components/Spinner';
import { useRouter } from 'next/navigation';







const LoginPage = () => {
    const cookie = new Cookies();
    const authService = new AuthService();
    const [ChkRememberMe, setRememberMe] = useState(true);
    const [loading, setLoading] = useState(false);
    const [LoginForm, setLoginForm] = useState({ userid: '', password: '', rememberMe: false });
    const cUSER_BASIC: string = USER_BASIC;
    const cSESSION_TOKEN: string = SESSION_TOKEN;
    const router = useRouter(); // 使用 useRouter 來進行路由導航


    // const authService = new AuthService();

    const handleLogin = async () => {
        try {
            const loginRequest: LoginReqIF = {
                userID: LoginForm.userid,
                Password: LoginForm.password,
                ExpDays: ChkRememberMe === true ? 7 : 1
            };

            setLoading(true); // 開啟 loading 狀態
            const res = await authService.Login(loginRequest);

            if (res.status === 200) {
                const loginResp: LoginRespIF = res.data;
                const expiresDate = new Date(loginResp.ExpDate + '');
                const cookieOptions = { path: '/', expires: expiresDate };
                console.log(loginResp);
                // 設置 cookie
                cookie.set(cUSER_BASIC, loginResp.UserBasic, cookieOptions);
                cookie.set(cSESSION_TOKEN, loginResp.JWT, cookieOptions);
                // 重新導向
                router.replace('/');
                window.location.href = '/';
            } else {
                alert(res.data.message);
            }
        } catch (err: any) {
            if (err.response && err.response.status.toString().startsWith('4')) {
                alert(err.response.data);
            } else {
                console.log('err', JSON.stringify(err));
                alert('系統錯誤，請稍後再試' + err.message);
            }
        }
        finally {
            setLoading(false);
        }
    };

    // TEST
    const handleTest = () => {
        setLoading(!loading);
    }

    return (
        <div className="flex items-center justify-center min-h-screen bg-gradient-to-r from-[#D5D5D3] to-[#E8E0D9]">
            {loading && <Spinner overlay={true} />}
            <div className="w-full max-w-md p-6 bg-[#F5F2EF] rounded-lg shadow-lg">
                {!loading && <h2 className="text-3xl font-semibold text-center text-[#6D7B7E]">CancerBook</h2>}
                {/* <h2 className="text-3xl font-semibold text-center text-gray-800">CancerBook</h2> */}
                <div className="mt-6">
                    <div>
                        <label className="block text-xl font-medium text-[#6D7B7E]">
                            使用者ID
                        </label>

                        <InputText className='block w-full px-4 py-2 mt-2 bg-[#EBE6E2] border border-[#D1C8C1] rounded-md focus:outline-none focus:ring-2 focus:ring-[#A99F97] focus:border-transparent'
                            value={LoginForm.userid} onChange={(e) => setLoginForm({ ...LoginForm, userid: e.target.value })} />
                    </div>
                    <div className="mt-4">
                        <label className="block text-xl font-medium text-[#6D7B7E]">密碼</label>


                        <InputText className='block w-full px-4 py-2 mt-2 bg-[#EBE6E2] border border-[#D1C8C1] rounded-md focus:outline-none focus:ring-2 focus:ring-[#A99F97] focus:border-transparent'
                            type="password" id="password" placeholder="請輸入密碼" required
                            value={LoginForm.password} onChange={(e) => setLoginForm({ ...LoginForm, password: e.target.value })} />
                    </div>
                    <div className='mt-6 flex justify-center'>
                        <ToggleButton className='mx-6 border' onLabel="記住我" offLabel="不要記住" onIcon="pi pi-check" offIcon="pi pi-times"
                            disabled={loading}
                            checked={ChkRememberMe} onChange={(e) => setRememberMe(e.value)} />
                    </div>
                    <div className="mt-6">
                        <Button label="登入" className="w-full px-4 py-2 text-lg font-semibold rounded-md bg-[#A99F97] hover:bg-[#8C8279] text-white"
                            onClick={handleLogin} />
                    </div>
                    {/* TEST BUTTON */}
                    {/* <div className="mt-6">
                        <Button label="TEST" className="w-full px-4 py-2 text-lg 
                            font-semibold text-white "
                            onClick={handleTest} />
                    </div> */}
                </div>
            </div>
        </div>
    );
};

export default LoginPage;