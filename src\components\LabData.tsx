import React, { useState, useEffect, useCallback } from 'react';
import { Checkbox } from 'primereact/checkbox';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { MultiSelect } from 'primereact/multiselect';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import { Chart } from 'primereact/chart';
import { LabDataIF } from '@/services/MRDataService';

interface LabDataProps {
    data: LabDataIF[] | undefined;
}

const LabData: React.FC<LabDataProps> = ({ data: labData }) => {
    // 控制是否顯示完整時間（包含時分）
    const [showTime, setShowTime] = useState(false);

    // 儲存按開單日分組的數據
    const [groupedByApplyDate, setGroupedByApplyDate] = useState<{ [date: string]: LabDataIF[] }>({});

    // 篩選狀態
    const [showAbnormalOnly, setShowAbnormalOnly] = useState(false);
    const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
    const [selectedTeams, setSelectedTeams] = useState<string[]>([]);
    const [selectedSpecimens, setSelectedSpecimens] = useState<string[]>([]);
    const [selectedSpecimenNames, setSelectedSpecimenNames] = useState<string[]>([]);

    // 篩選選項
    const [filterOptions, setFilterOptions] = useState<{
        groups: { label: string; value: string }[];
        teams: { label: string; value: string }[];
        specimens: { label: string; value: string }[];
        specimenNames: { label: string; value: string }[];
    }>({
        groups: [],
        teams: [],
        specimens: [],
        specimenNames: []
    });

    // 圖表相關狀態
    const [chartVisible, setChartVisible] = useState(false);
    const [selectedChartData, setSelectedChartData] = useState<{
        title: string;
        data: LabDataIF[];
        unit: string;
        isNumeric: boolean;  // 新增：判斷是否為數值型數據
    } | null>(null);

    // 轉換民國年日期格式為西元年
    const convertTWDate = (twDate: string) => {
        if (!twDate) return new Date();
        const year = parseInt(twDate.substring(0, 3)) + 1911;
        const month = parseInt(twDate.substring(3, 5)) - 1;
        const day = parseInt(twDate.substring(5, 7));
        const hour = parseInt(twDate.substring(7, 9));
        const minute = parseInt(twDate.substring(9, 11));
        return new Date(year, month, day, hour, minute);
    };

    // 轉換西元年日期格式為民國年 2020/11/28 => 109/11/28 
    const convertTWDateToTWDateString = (date: Date) => {
        const year = date.getFullYear() - 1911;
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return `${year.toString().padStart(3, '0')}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    };

    // 格式化日期顯示
    const formatDate = (date: Date) => {
        if (showTime) {
            return date.toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            return date.toLocaleDateString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        }
    };

    // 格式化開單日期（用於群組標題）
    const formatApplyDate = (date: Date) => {
        return date.toLocaleDateString('zh-TW', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    };

    // 篩選數據
    const filterData = (data: LabDataIF[]) => {
        return data.filter(item => {
            // 異常值篩選
            if (showAbnormalOnly) {
                const isAbnormal = item.LAB_ABNORMAL_FLAG === '1' || item.LAB_ABNORMAL_FLAG === '2';
                if (!isAbnormal) {
                    return false;
                }
            }

            // 群組篩選
            if (selectedGroups.length > 0 && !selectedGroups.includes(item.LAB_GroupHName)) {
                return false;
            }

            // 組別篩選
            if (selectedTeams.length > 0 && !selectedTeams.includes(item.LAB_TeamName)) {
                return false;
            }

            // 檢體名稱篩選
            if (selectedSpecimens.length > 0 && !selectedSpecimens.includes(item.SPECIMEN_NAME || item.LAB_SpecimenName)) {
                return false;
            }

            // 檢驗名稱篩選
            if (selectedSpecimenNames.length > 0 && !selectedSpecimenNames.includes(item.LAB_SpecimenName)) {
                return false;
            }

            return true;
        });
    };

    // 獲取篩選後的群組數據
    const getFilteredGroupedData = useCallback(() => {
        if (!labData) return {};

        const filteredData = filterData(labData);

        // 按開單日分組
        const grouped = filteredData.reduce((acc, item) => {
            const applyDate = convertTWDate(item.ApplyDateTime);
            const dateKey = formatApplyDate(applyDate);

            if (!acc[dateKey]) {
                acc[dateKey] = [];
            }
            acc[dateKey].push(item);
            return acc;
        }, {} as { [date: string]: LabDataIF[] });

        // 對每個日期內的數據按確認時間排序
        Object.keys(grouped).forEach(dateKey => {
            grouped[dateKey].sort((a, b) =>
                convertTWDate(a.CONFIRMED_DateTime).getTime() - convertTWDate(b.CONFIRMED_DateTime).getTime()
            );
        });

        // 按日期排序（最新的在前）
        const sortedDates = Object.keys(grouped).sort((a, b) =>
            new Date(b).getTime() - new Date(a).getTime()
        );

        const sortedGrouped: { [date: string]: LabDataIF[] } = {};
        sortedDates.forEach(date => {
            sortedGrouped[date] = grouped[date];
        });

        return sortedGrouped;
    }, [labData, showAbnormalOnly, selectedGroups, selectedTeams, selectedSpecimens, selectedSpecimenNames]);

    useEffect(() => {
        if (!labData) return;

        // 建立篩選選項
        const groups = [...new Set(labData.map(item => item.LAB_GroupHName).filter(Boolean))];
        const teams = [...new Set(labData.map(item => item.LAB_TeamName).filter(Boolean))];
        const specimens = [...new Set(labData.map(item => item.SPECIMEN_NAME || item.LAB_SpecimenName).filter(Boolean))];
        const specimenNames = [...new Set(labData.map(item => item.LAB_SpecimenName).filter(Boolean))];

        setFilterOptions({
            groups: groups.map(group => ({ label: group, value: group })),
            teams: teams.map(team => ({ label: team, value: team })),
            specimens: specimens.map(specimen => ({ label: specimen, value: specimen })),
            specimenNames: specimenNames.map(name => ({ label: name, value: name }))
        });

        // 按開單日（ApplyDateTime）分組
        const grouped = labData.reduce((acc, item) => {
            const applyDate = convertTWDate(item.ApplyDateTime);
            const dateKey = formatApplyDate(applyDate);

            if (!acc[dateKey]) {
                acc[dateKey] = [];
            }
            acc[dateKey].push(item);
            return acc;
        }, {} as { [date: string]: LabDataIF[] });

        // 對每個日期內的數據按確認時間排序
        Object.keys(grouped).forEach(dateKey => {
            grouped[dateKey].sort((a, b) =>
                convertTWDate(a.CONFIRMED_DateTime).getTime() - convertTWDate(b.CONFIRMED_DateTime).getTime()
            );
        });

        // 按日期排序（最新的在前）
        const sortedDates = Object.keys(grouped).sort((a, b) =>
            new Date(b).getTime() - new Date(a).getTime()
        );

        const sortedGrouped: { [date: string]: LabDataIF[] } = {};
        sortedDates.forEach(date => {
            sortedGrouped[date] = grouped[date];
        });

        setGroupedByApplyDate(sortedGrouped);
    }, [labData]);

    // 當篩選條件改變時，更新顯示的數據
    useEffect(() => {
        const filteredGrouped = getFilteredGroupedData();
        setGroupedByApplyDate(filteredGrouped);
    }, [getFilteredGroupedData]);

    // 桌面版表格佈局（改為 PrimeReact DataTable）
    const renderTableLayout = (items: LabDataIF[]) => {
        const timeBody = (row: LabDataIF) => formatDate(convertTWDate(row.CONFIRMED_DateTime));
        const groupBody = (row: LabDataIF) => row.LAB_GroupHName || '-';
        const teamBody = (row: LabDataIF) => <span className="font-medium">{row.LAB_TeamName || '-'}</span>;
        const specimenBody = (row: LabDataIF) => row.SPECIMEN_NAME || row.LAB_SpecimenName || '-';
        const resultBody = (row: LabDataIF) => (
            <div
                className="cursor-pointer bg-blue-200 hover:bg-blue-50 p-2 rounded transition-colors"
                onMouseEnter={(e) => e.currentTarget.classList.add('bg-blue-50')}
                onMouseLeave={(e) => e.currentTarget.classList.remove('bg-blue-50')}
                onClick={() => handleShowChart(row)}
                title="點擊查看趨勢圖"
            >
                <span className={`font-medium ${row.LAB_ABNORMAL_FLAG === '1' ? 'text-red-600' : row.LAB_ABNORMAL_FLAG === '2' ? 'text-blue-600' : 'text-gray-900'}`}>
                    {row.ResultValue || '-'}
                </span>
                {row.Unit && <span className="text-xs text-gray-500 ml-1">({row.Unit})</span>}
            </div>
        );
        const unitBody = (row: LabDataIF) => row.Unit || '-';
        const abnormalBody = (row: LabDataIF) => (
            row.LAB_ABNORMAL_FLAG ? (
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${row.LAB_ABNORMAL_FLAG === '1' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`}>
                    {row.LAB_ABNORMAL_FLAG === '1' ? '異常' : '正常'}
                </span>
            ) : <span className="text-gray-400">-</span>
        );
        const commentBody = (row: LabDataIF) => row.Comment || '-';

        return (
            <div className="overflow-x-auto">
                <DataTable
                    value={items}
                    stripedRows
                    showGridlines
                    size="small"
                    className="p-datatable-sm"
                >
                    <Column header="檢驗時間" body={timeBody} headerClassName="text-sm" bodyClassName="px-4 py-3 text-sm text-gray-900 w-[10%]" />
                    <Column header="群組" body={groupBody} headerClassName="text-sm" bodyClassName="text-base text-gray-700 w-[13%]" />
                    <Column header="組別" body={teamBody} headerClassName="text-sm" bodyClassName="px-4 py-3 text-base text-gray-700 w-[10%]" />
                    <Column header="檢驗名稱" body={specimenBody} headerClassName="text-sm" bodyClassName="px-4 py-3 text-base text-gray-700 w-[13%]" />
                    <Column header="檢驗結果值" body={resultBody} headerClassName="text-sm" bodyClassName="px-4 py-3 text-base w-[15%]" />
                    {/* <Column header="單位" body={unitBody} headerClassName="text-sm" bodyClassName="px-4 py-3 text-sm text-gray-600 w-[9%]" /> */}
                    <Column header="異常註記" body={abnormalBody} headerClassName="text-sm" bodyClassName="px-4 py-3 text-sm w-[6%]" />
                    <Column header="備註" body={commentBody} headerClassName="text-sm" bodyClassName="px-4 py-3 text-sm text-gray-600" />
                </DataTable>
            </div>
        );
    };

    // 手機版卡片佈局
    const renderCardLayout = (items: LabDataIF[]) => {
        return (
            <div className="grid grid-cols-1 gap-4 p-4">
                {items.map((item, index) => (
                    <div key={`${item.OrdNo}-${index}`} className="card border rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow">
                        <div className="p-4">
                            <div className="flex justify-between items-start mb-3">
                                <div className="text-sm text-gray-600">
                                    {formatDate(convertTWDate(item.CONFIRMED_DateTime))}
                                </div>
                                {item.LAB_ABNORMAL_FLAG && (
                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${item.LAB_ABNORMAL_FLAG === '1'
                                        ? 'bg-red-100 text-red-800'
                                        : 'bg-blue-100 text-blue-800'
                                        }`}>
                                        異常
                                    </span>
                                )}
                            </div>

                            <div className="space-y-0">
                                <div>
                                    <span className="text-xs text-gray-500">群組：</span>
                                    <span className="text-sm font-medium">{item.LAB_GroupHName || '-'}</span>
                                </div>

                                <div>
                                    <span className="text-xs text-gray-500">組別：</span>
                                    <span className="text-sm font-medium">{item.LAB_TeamName || '-'}</span>
                                </div>

                                <div>
                                    <span className="text-xs text-gray-500">檢體名稱：</span>
                                    <span className="text-sm">{item.SPECIMEN_NAME || item.LAB_SpecimenName || '-'}</span>
                                </div>

                                <div>
                                    <span className="text-xs text-gray-500">檢驗結果值：</span>
                                    <div className="flex items-center gap-2">
                                        <span className={`text-sm font-medium ${item.LAB_ABNORMAL_FLAG === '1' ? 'text-red-600' :
                                            item.LAB_ABNORMAL_FLAG === '2' ? 'text-blue-600' :
                                                'text-gray-900'
                                            }`}>
                                            {item.ResultValue || '-'}
                                        </span>
                                        {item.Unit && <span className="text-xs text-gray-500">({item.Unit})</span>}
                                        <Button
                                            icon="pi pi-chart-line"
                                            size="small"
                                            text
                                            severity="secondary"
                                            onClick={() => handleShowChart(item)}
                                            className="p-1"
                                            title="查看趨勢圖"
                                        />
                                    </div>
                                </div>

                                {item.Comment && (
                                    <div>
                                        <span className="text-xs text-gray-500">備註：</span>
                                        <span className="text-sm text-gray-700">{item.Comment}</span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        );
    };

    if (!labData || labData.length === 0) {
        return (
            <div className="w-full p-8 text-center text-gray-500">
                無檢驗數據
            </div>
        );
    }

    // 獲取篩選後的總數量
    const filteredTotalCount = Object.values(groupedByApplyDate).reduce((sum, items) => sum + items.length, 0);

    // 處理圖表顯示
    const handleShowChart = (item: LabDataIF) => {
        // 獲取相同檢體、檢驗項目的所有數據
        /*
        LAB_GroupHName: string;      // 群組
        LAB_TeamName: string;        // 組別
        LAB_SpecimenName: string;    // 檢體
        SPECIMEN_NAME: string;       // 檢驗名稱
        ResultValue: string;         // 檢驗結果值
        */
        const sameTestData = labData?.filter(labItem =>
            labItem.LAB_SpecimenName === item.LAB_SpecimenName &&
            labItem.SPECIMEN_NAME === item.SPECIMEN_NAME
        ) || [];
        console.log(sameTestData);

        if (sameTestData.length > 0) {
            // 按時間排序
            const sortedData = sameTestData.sort((a, b) =>
                convertTWDate(a.CONFIRMED_DateTime).getTime() - convertTWDate(b.CONFIRMED_DateTime).getTime()
            );

            // 檢查是否為數值型數據
            const isNumeric = sortedData.every(labItem => {
                const value = labItem.ResultValue?.trim();
                if (!value) return false;

                // 檢查是否為純數字（允許小數點、負號）
                // 排除包含 +、-、>、<、± 等特殊符號的情況
                const numericPattern = /^-?\d+(\.\d+)?$/;
                return numericPattern.test(value);
            });

            setSelectedChartData({
                title: `${item.LAB_TeamName} - ${item.LAB_SpecimenName} - ${item.SPECIMEN_NAME}`,
                data: sortedData,
                unit: item.Unit || '',
                isNumeric: isNumeric
            });
            setChartVisible(true);
        }
    };

    // 準備圖表數據
    const getChartData = () => {
        if (!selectedChartData) return {};

        const chartData = {
            labels: selectedChartData.data.map(item =>
                formatDate(convertTWDate(item.CONFIRMED_DateTime))
            ),
            datasets: [
                {
                    label: selectedChartData.title,
                    data: selectedChartData.data.map(item => {
                        const value = parseFloat(item.ResultValue);
                        return isNaN(value) ? 0 : value;
                    }),
                    borderColor: '#2196F3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    tension: 0.4,
                    pointBackgroundColor: selectedChartData.data.map(item => {
                        if (item.LAB_ABNORMAL_FLAG === '1') return '#ff4444';
                        if (item.LAB_ABNORMAL_FLAG === '2') return '#2196F3';
                        return '#2196F3';
                    }),
                    pointRadius: 6,
                    pointHoverRadius: 8
                }
            ]
        };

        return chartData;
    };

    // 準備表格數據
    const getTableData = () => {
        if (!selectedChartData) return [];
        // 依照日期排序 desc
        const sortedData = selectedChartData.data.sort((a, b) =>
            convertTWDate(b.CONFIRMED_DateTime).getTime() - convertTWDate(a.CONFIRMED_DateTime).getTime()
        );
        return sortedData.map(item => ({
            date: formatDate(convertTWDate(item.CONFIRMED_DateTime)),
            result: item.ResultValue || '-',
            unit: item.Unit || '-',
            abnormal: item.LAB_ABNORMAL_FLAG === '1' ? '異常' : item.LAB_ABNORMAL_FLAG === '2' ? '正常' : '-',
            comment: item.Comment || '-'
        }));
    };

    // 圖表選項
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: selectedChartData?.title || '',
                font: {
                    size: 16,
                    weight: 'bold'
                }
            },
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    label: function (context: any) {
                        const dataIndex = context.dataIndex;
                        const item = selectedChartData?.data[dataIndex];
                        let label = `${context.dataset.label}: ${context.parsed.y}`;
                        if (selectedChartData?.unit) {
                            label += ` ${selectedChartData.unit}`;
                        }
                        if (item?.LAB_ABNORMAL_FLAG) {
                            label += ` (${item.LAB_ABNORMAL_FLAG === '1' ? '異常' : '正常'})`;
                        }
                        return label;
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: false,
                title: {
                    display: true,
                    text: selectedChartData?.unit || '數值'
                }
            },
            x: {
                title: {
                    display: true,
                    text: '檢驗時間'
                },
                ticks: {
                    maxRotation: 45,
                    minRotation: 45
                }
            }
        }
    };

    return (
        <div className="w-full">
            {/* 控制選項和篩選器 */}
            <div className="mb-4 p-4 border rounded-lg bg-gray-50">
                <div className="space-y-4">
                    {/* 基本控制選項 */}
                    <div className="flex items-center justify-between">
                        <div className="flex flex-items items-center gap-4">
                            <div className="flex items-center gap-2">
                                <Checkbox
                                    inputId="cb-show-time"
                                    checked={showTime}
                                    onChange={(e) => setShowTime(e.checked || false)}
                                />
                                <label
                                    htmlFor="cb-show-time"
                                    className="cursor-pointer text-base font-medium"
                                >
                                    顯示完整時間
                                </label>
                            </div>
                            <div className="flex items-center gap-2">
                                <Checkbox
                                    inputId="cb-abnormal-only"
                                    checked={showAbnormalOnly}
                                    onChange={(e) => setShowAbnormalOnly(e.checked || false)}
                                />
                                <label
                                    htmlFor="cb-abnormal-only"
                                    className="cursor-pointer text-base font-medium text-red-600"
                                >
                                    只顯示異常值
                                </label>
                                <span className="text-xs text-gray-500">
                                    ({/* 異常統計 */}
                                    {Object.values(groupedByApplyDate).flat().filter((r: LabDataIF) => r.LAB_ABNORMAL_FLAG === '1' || r.LAB_ABNORMAL_FLAG === '2').length}/{filteredTotalCount})
                                </span>
                            </div>
                        </div>
                        <div className="text-sm text-gray-600">
                            篩選後：{filteredTotalCount} / {labData.length} 筆
                        </div>
                    </div>

                    {/* 四層篩選器 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                群組
                            </label>
                            <MultiSelect
                                value={selectedGroups}
                                onChange={(e) => setSelectedGroups(e.value || [])}
                                options={filterOptions.groups}
                                placeholder="選擇群組"
                                className="w-full"
                                showClear
                                filter
                                filterPlaceholder="搜尋群組..."
                                showSelectAll={false}
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                組別
                            </label>
                            <MultiSelect
                                value={selectedTeams}
                                onChange={(e) => setSelectedTeams(e.value || [])}
                                options={filterOptions.teams}
                                placeholder="選擇組別"
                                className="w-full"
                                showClear
                                filter
                                filterPlaceholder="搜尋組別..."
                                showSelectAll={false}
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                檢體
                            </label>
                            <MultiSelect
                                value={selectedSpecimenNames}
                                onChange={(e) => setSelectedSpecimenNames(e.value || [])}
                                options={filterOptions.specimenNames}
                                placeholder="選擇檢體"
                                className="w-full"
                                showClear
                                filter
                                filterPlaceholder="搜尋檢體..."
                                showSelectAll={false}
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                檢驗名稱
                            </label>
                            <MultiSelect
                                value={selectedSpecimens}
                                onChange={(e) => setSelectedSpecimens(e.value || [])}
                                options={filterOptions.specimens}
                                placeholder="選擇檢驗名稱"
                                className="w-full"
                                showClear
                                filter
                                filterPlaceholder="搜尋檢驗名稱..."
                                showSelectAll={false}
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* 檢驗數據清單 - 使用 Accordion 實現可收折群組 */}
            <Accordion multiple className="space-y-2">
                {Object.entries(groupedByApplyDate).map(([applyDate, items], index) => (
                    <AccordionTab
                        key={applyDate}
                        header={
                            <div className="flex items-center justify-between w-full">
                                <div className="flex items-center gap-3">
                                    <span className="text-lg font-bold text-blue-800">
                                        開單日期：{convertTWDateToTWDateString(new Date(applyDate))}
                                    </span>
                                    <span className="text-sm text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                                        {items.length} 筆
                                    </span>
                                </div>
                            </div>
                        }
                        className="border rounded-lg bg-white overflow-hidden"
                    >
                        {/* 響應式佈局：桌面版顯示 DataTable，手機版顯示卡片 */}
                        <div className="hidden md:block">
                            {renderTableLayout(items)}
                        </div>
                        <div className="md:hidden">
                            {renderCardLayout(items)}
                        </div>
                    </AccordionTab>
                ))}
            </Accordion>

            {/* 圖表彈出視窗 */}
            <Dialog
                visible={chartVisible}
                onHide={() => setChartVisible(false)}
                header={selectedChartData?.title || '檢驗值趨勢圖'}
                className="w-[90vw] md:w-[80vw] lg:w-[70vw]"
                maximizable
                closeOnEscape
                closable
                dismissableMask={true}
            >
                <div className="p-4">
                    {selectedChartData && (
                        <div className="space-y-4">
                            <div className="text-sm text-gray-600">
                                <p>檢驗項目：{selectedChartData.title}</p>
                                <p>單位：{selectedChartData.unit || '無'}</p>
                                <p>數據點數：{selectedChartData.data.length}</p>
                                <p>數據類型：{selectedChartData.isNumeric ? '數值型' : '文字型'}</p>
                            </div>

                            {selectedChartData.isNumeric ? (
                                // 數值型數據顯示圖表
                                <div className="h-[400px]">
                                    <Chart
                                        type="line"
                                        data={getChartData()}
                                        options={chartOptions}
                                        className="w-full h-full"
                                    />
                                </div>
                            ) : (
                                // 文字型數據顯示表格
                                <div className="overflow-x-auto">
                                    <DataTable
                                        value={getTableData()}
                                        stripedRows
                                        showGridlines
                                        size="small"
                                        className="p-datatable-sm"
                                    >
                                        <Column header="檢驗時間" field="date" headerClassName="text-sm" bodyClassName="px-4 py-3 text-base text-gray-900 font-semibold" />
                                        <Column header="檢驗結果值" field="result" headerClassName="text-sm" bodyClassName="px-4 py-3 text-base font-semibold" />
                                        <Column header="單位" field="unit" headerClassName="text-sm" bodyClassName="px-4 py-3 text-sm text-gray-600" />
                                        <Column header="異常註記" field="abnormal" headerClassName="text-sm" bodyClassName="px-4 py-3 text-sm" />
                                        <Column header="備註" field="comment" headerClassName="text-sm" bodyClassName="px-4 py-3 text-sm text-gray-600" />
                                    </DataTable>
                                </div>
                            )}

                            <div className="text-xs text-gray-500 text-center">
                                {selectedChartData.isNumeric ? (
                                    <>
                                        <p>異常值用紅色標示，正常值用藍色標示</p>
                                        <p>滑鼠懸停在數據點上可查看詳細信息</p>
                                    </>
                                ) : (
                                    <p>文字型檢驗結果以表格形式呈現</p>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </Dialog>
        </div>
    );
};

export default LabData;
