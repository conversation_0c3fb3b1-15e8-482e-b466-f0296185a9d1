'use client';

import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import React, { useState } from 'react';
import AesEncoder from '@/modules/AesEncoder';
import { Card } from 'primereact/card';

const AesTest: React.FC = () => {
    const [plainText, setPlainText] = useState('');
    const [encryptedText, setEncryptedText] = useState('');
    const [cipherText, setCipherText] = useState('');
    const [decryptedText, setDecryptedText] = useState('');
    const [customKey, setCustomKey] = useState('5A77E83765A5BAB37EE2673169B61878');

    const aesEncoder = new AesEncoder();

    const handleEncrypt = () => {
        try {
            const encrypted = customKey
                ? aesEncoder.EncryptStringCustom(plainText, customKey)
                : aesEncoder.EncryptString(plainText);
            setEncryptedText(encrypted);
        } catch (error) {
            console.error('加密錯誤:', error);
            setEncryptedText('加密錯誤，請檢查輸入和密鑰');
        }
    };

    const handleDecrypt = () => {
        try {
            const decrypted = customKey
                ? aesEncoder.DecryptStringCustom(cipherText, customKey)
                : aesEncoder.DecryptString(cipherText);
            setDecryptedText(decrypted);
        } catch (error) {
            console.error('解密錯誤:', error);
            setDecryptedText('解密錯誤，請檢查輸入和密鑰');
        }
    };

    return (
        <div className="p-4 flex flex-col gap-4 w-full  max-w-screen overflow-x-hidden max-h-screen">
            <h1 className="text-2xl font-bold mb-2">AES 加密解密測試</h1>

            <Card title="自定義密鑰" className="w-full mb-2">
                <InputText
                    value={customKey}
                    onChange={(e) => setCustomKey(e.target.value)}
                    placeholder="輸入自定義密鑰 (可選，留空則使用環境變數中的密鑰)"
                    className="w-full"
                />
                <div className="text-sm text-gray-500 mt-2">
                    提示：密鑰必須至少為32個字符長度的十六進制字串
                </div>
            </Card>

            {/* Encrypt TEST */}
            <Card title="加密測試" className="w-full mb-2">
                <div className="flex flex-col gap-2">
                    <div>
                        <label htmlFor="plainText" className="block mb-1">原文</label>
                        <InputText
                            id="plainText"
                            value={plainText}
                            onChange={(e) => setPlainText(e.target.value)}
                            placeholder="請輸入要加密的文字"
                            className="w-full"
                        />
                    </div>

                    <div className="my-2">
                        <Button label="加密" onClick={handleEncrypt} className="w-full" />
                    </div>

                    <div>
                        <label htmlFor="encryptedText" className="block mb-1">加密結果</label>
                        <div className="flex flex-col">
                            <InputText
                                id="encryptedText"
                                value={encryptedText}
                                readOnly
                                className="w-full"
                            />
                        </div>
                    </div>
                </div>
            </Card>

            {/* Decrypt TEST */}
            <Card title="解密測試" className="w-full mb-2">
                <div className="flex flex-col gap-2">
                    <div>
                        <label htmlFor="cipherText" className="block mb-1">密文</label>
                        <InputText
                            id="cipherText"
                            value={cipherText}
                            onChange={(e) => setCipherText(e.target.value)}
                            placeholder="請輸入要解密的文字"
                            className="w-full"
                        />
                    </div>

                    <div className="my-2">
                        <Button label="解密" onClick={handleDecrypt} className="w-full" />
                    </div>

                    <div>
                        <label htmlFor="decryptedText" className="block mb-1">解密結果</label>
                        <InputText
                            id="decryptedText"
                            value={decryptedText}
                            readOnly
                            className="w-full"
                        />
                    </div>
                </div>
            </Card>
        </div>
    );
};

export default AesTest;