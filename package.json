{"name": "cancer-mrallshow-nextjs-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@reduxjs/toolkit": "^2.2.7", "axios": "^1.7.3", "chart.js": "^4.4.6", "chartjs-plugin-datalabels": "^2.2.0", "crypto-js": "^4.2.0", "next": "^14.2.15", "next-pwa": "^5.6.0", "primeflex": "^3.3.1", "primeicons": "^7.0.0", "primereact": "^10.8.0", "react": "^18", "react-dom": "^18", "react-redux": "^9.1.2", "react-router-dom": "^6.26.0", "redux": "^5.0.1", "tailwindcss-animate": "^1.0.7", "universal-cookie": "^7.2.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "typescript": "^5"}}