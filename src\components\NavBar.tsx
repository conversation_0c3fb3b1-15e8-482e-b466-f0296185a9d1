'use client';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import { CnacerIcon, MountainIcon, SearchIcon, UserIcon } from './icons';
import { useEffect, useState, useContext, useRef, useMemo } from 'react';
// AutoComplete
import { AutoComplete, AutoCompleteCompleteEvent } from "primereact/autocomplete";
import { AuthService } from '@/services/AuthService';
import { SplitButton } from 'primereact/splitbutton';
import Cookies from 'universal-cookie';
import { Menubar } from 'primereact/menubar';
import { MenuItem } from 'primereact/menuitem';
import { Badge } from 'primereact/badge';
import CaseSearch from '@/components/CaseSearch';
import { TabItemIF } from '@/components/Tab/tabIF';
import { setToastRef, showError, showInfo } from '@/components/Toast';
import { Toast } from 'primereact/toast';
import { getIdPossibleSuffixes } from '@/utils/AutoComplete';
import './NavBar.css';
import BlankFrame from './Tab/BlankFrame';
import Image from 'next/image';
import { Avatar } from 'primereact/avatar';
import { Menu } from 'primereact/menu';


interface NavBarProps {
  className?: string;
  tabPageRef: React.MutableRefObject<{
    addTab: (content: TabItemIF) => void;
    removeTab: (title: string) => void;
    clearTabs: () => void;
    tabs: TabItemIF[];
    setTabs: (tabs: TabItemIF[]) => void;
  } | null>;
}

const NavBar: React.FC<NavBarProps> = ({ className, tabPageRef }) => {
  const authService = useMemo(() => new AuthService(), []);
  const cookie = useMemo(() => new Cookies(), []);
  let [userBasic, setUserBasic] = useState<UserBasicIF>();

  useEffect(() => {
    const checkLoginAndSetUser = async () => {
      // 檢查登入狀態
      await authService.CheckLogin();
      // 賦值
      const userBasicData = cookie.get('USER_BASIC');
      setUserBasic(userBasicData);
      // 將UserBasic 的userImage 改成 https://www.google.com/images/branding/googlelogo/2x/googlelogo_color_92x30dp.png
      // userBasicData.userImage = "/Home.png";
      console.log(userBasicData);
    };

    checkLoginAndSetUser();
  }, [authService, cookie]);

  interface searchPatient {
    name: string;
    code: string;
  }
  // 搜尋身份證字號 自動遞補
  const [searchItems, setSearchItems] = useState<searchPatient[]>([]);
  const [searchItem, setSearchItem] = useState<string>();

  const search = (event: { query?: string }) => {
    // 自動填補身分證字號
    const idList = event.query ? getIdPossibleSuffixes(event.query.toUpperCase()) : [];
    setSearchItems(idList.map((idValue, index) => ({ name: idValue, code: idValue })));
    console.log(searchItems);

  }
  // handleSearchPatient
  const handleSearchPatient = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && searchItem) {
      console.log('searchItem:', searchItem);
      // showInfo('搜尋個案中...');
      // 搜尋個案
      const tab: TabItemIF = {
        title: '個案查詢',
        tabID: '個案查詢',
        content: <CaseSearch key={Date.now()} tabPageRef={tabPageRef} mrNoOrName={searchItem} />,
      };
      // 寫 key={Date.now()} 做到強制重新渲染
      addTab(tab);
    }
  }


  const userItems = [
    {
      label: '登出',
      icon: 'pi pi-sign-out',
      command: () => {
        console.log('登出');
        cookie.remove('USER_BASIC');
        cookie.remove('SESSION_TOKEN');
        // 直接移除 beforeunload 事件監聽器，避免攔截登出
        window.removeEventListener('beforeunload', () => { });
        window.location.href = "/Login";
      }
    }
  ];

  // -----------------小工具V---------------

  // ------------小工具^-----------------------

  const addTab = (tab: TabItemIF) => {
    if (tabPageRef.current) {
      tabPageRef.current.addTab(tab);
    } else {
      showError('Tab page reference is not available');
    }
  }

  const itemRenderer = (item: any) => (
    <a className="flex align-items-center p-menuitem-link">
      <span className={item.icon} />
      <span className="mx-2">{item.label}</span>
      {item.badge && <Badge className="ml-auto" value={item.badge} />}
      {item.shortcut && <span className="ml-auto border-1 surface-border border-round surface-100 text-xs p-1">{item.shortcut}</span>}
    </a>
  );
  const items = [

    {
      label: '個案查詢',
      icon: 'pi pi-search',
      command: () => {
        const tab: TabItemIF = {
          title: '個案查詢',
          tabID: '個案查詢',
          content: <CaseSearch tabPageRef={tabPageRef} />,
        };
        addTab(tab);
      }

    },
    {
      label: ' 新增頁面',
      icon: 'pi pi-star',
      command: () => {
        // const tab: TabItemIF = { title: '新增頁面', tabID: 'test', content: <BlankFrame url="http://hlcandbsvr/CancerPlan/TeamMeeting?MRNo=U200898608&CancerName=ALL&Edit=N&DocNo=&UserID=V121562835" /> };
        const tab: TabItemIF = { title: '新增頁面', tabID: 'test', content: <BlankFrame /> };
        addTab(tab);
      }
    },
    {
      label: 'Projects',
      icon: 'pi pi-search',
      items: [
        {
          label: 'Core',
          icon: 'pi pi-bolt',
          shortcut: '⌘+S',
          template: itemRenderer
        },
        {
          label: 'Blocks',
          icon: 'pi pi-server',
          shortcut: '⌘+B',
          template: itemRenderer
        },
        {
          label: 'UI Kit',
          icon: 'pi pi-pencil',
          shortcut: '⌘+U',
          template: itemRenderer
        },
        {
          separator: true
        },
        {
          label: 'Templates',
          icon: 'pi pi-palette',
          items: [
            {
              label: 'Apollo',
              icon: 'pi pi-palette',
              badge: 2,
              template: itemRenderer
            },
            {
              label: 'Ultima',
              icon: 'pi pi-palette',
              badge: 3,
              template: itemRenderer
            },
            {
              label: 'Contact',
              icon: 'pi pi-envelope',
              badge: 5,
              template: itemRenderer
            }
          ]
        }
      ]
    }
  ];
  // const start = <img alt="logo" src="https://primefaces.org/cdn/primereact/images/logo.png" height="40" className="mr-2"></img>;
  const start = (
    <Button
      className="p-button-text p-0 "
      onClick={() => {
        const tab: TabItemIF = {
          title: 'Dashboard',
          tabID: 'Dashboard',
        };
        addTab(tab);
      }}
    >
      <div className="items-center gap-2 flex mr-2">
        <CnacerIcon className="h-8 w-8  hidden md:flex" />
        <span className="text-xl p-button-label font-bold">CancerBook</span>
      </div>
    </Button>
  );

  const menuRef = useRef<Menu>(null);

  const end = (
    <div className="flex items-center justify-center pr-6">
      <AutoComplete
        field="name"
        value={searchItem}
        suggestions={searchItems}
        completeMethod={search}
        onChange={(e) => setSearchItem(e.value)}
        onKeyDown={(e) => handleSearchPatient(e as React.KeyboardEvent<HTMLInputElement>)}
        type="search"
        placeholder="快速搜尋"
        className="h-8 text-sm"
        inputClassName="w-[80px] md:w-[200px]"
        panelClassName="z-50"
      />
      <div className="flex items-center justify-end ml-1 md:ml-2 max-w-[150px] md:max-w-[200px]">
        <Button
          className="p-button-rounded items-center gap-1 md:gap-2 flex px-2 py-1 outline-none w-full"
          onClick={(e) => menuRef.current?.toggle(e)}
          outlined
        >
          {userBasic && userBasic.userImage &&
            <Avatar
              image={userBasic.userImage}
              shape="circle"
              size="normal"
              className="hidden md:flex"
            />
          }
          <span className="font-semibold truncate">{userBasic?.userName || ''}</span>
        </Button>
        <Menu
          ref={menuRef}
          model={userItems}
          popup
          className="z-50"
        />
      </div>
    </div>
  );

  return (
    <div className="card">
      <Toast ref={(ref) => setToastRef(ref)} />
      <Menubar
        model={items}
        start={start}
        end={end}
        className='h-14 rounded-b-3xl w-full'
      />
    </div>
  );
};

export default NavBar;