'use client';
import React, { createContext, useEffect, useState, useCallback, useMemo } from 'react';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { GenService } from '@/services/GenService';
import { CancerNameMappingIF } from '@/models/Gen/CancerTypes';
import { Button } from 'primereact/button';
// DataTable
import { DataTable } from 'primereact/datatable';
import { CancerBasicCondition } from '@/models/Conditions';
import { MRDataService } from '@/services/MRDataService';
import { Column } from 'primereact/column';
import { convertToTWDateHyphenFromDateString } from '@/utils/DataFormatter';
import { setToastRef, showError, showInfo } from './Toast';
import { Toast } from 'primereact/toast';
import { it } from 'node:test';
import Spinner from './Spinner';
import { TabItemIF } from '@/components/Tab/tabIF';
import Cookies from 'universal-cookie';
import BlankFrame from '@/components/Tab/BlankFrame';
import { SelectButton } from 'primereact/selectbutton';
import CaseDetail from '@/components/CaseDetail/CaseDetail';
import { Dropdown } from 'primereact/dropdown';
import { CancerBasicIF } from '@/models/MRData';



interface CaseSearchProps {
    action?: string;
    mrNo?: number;
    name?: string;
    mrNoOrName?: string;

    tabPageRef: React.MutableRefObject<{
        addTab: (content: TabItemIF) => void;
        removeTab: (tabID: string) => void;
        clearTabs: () => void;
    } | null>;
}
interface CancerTypeIF {
    name?: string;
    code?: string;
}

const CaseSearch: React.FC<CaseSearchProps> = ({ action, mrNo, name, mrNoOrName, tabPageRef }) => {
    const genService = new GenService();
    const mrDataService = useMemo(() => new MRDataService(), []);

    interface MultiSelectIF {
        name: string;
        code: string;
    }
    const addTab = (tab: TabItemIF) => {
        if (tabPageRef.current) {
            tabPageRef.current.addTab(tab);
        } else {
            showError('Tab page reference is not available');
        }
    }


    // 從Cookie中抓 USER_BASIC 資料
    const cookie = new Cookies();
    const UserBasic: UserBasicIF = cookie.get('USER_BASIC');
    const [cancerPatients, setCancerPatients] = useState<CancerPersonBasicIF[] | null>([]);


    interface CancerPersonBasicIF {
        mrNo: string;
        name: string;
        birthday: string;
        sex: string;
        add: string;
        death: boolean;
        tel: string;
        firstDate: string;
        cancerData: PersonCancerIF[];
    }
    interface PersonCancerIF {
        cancerNo: string;
        icd9Code: string;
        initialICD9Date: string;
        firstCat: string;
        cancerType: string;
        cancerTypeC: string;
    }

    const [searchData, setSearchData] = useState({
        mrno: mrNo?.toString() || '',
        name: name || '',
        mrNoOrName: mrNoOrName || '',
        selectedCancerType: [] as MultiSelectIF[],
    });
    const handleSearchDataChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target;
        setSearchData((prevSearchData) => ({
            ...prevSearchData,
            [name]: value,
        }));
    };
    const [cancerTypes, setCancerTypes] = useState<CancerTypeIF[]>([]);
    const getCancerTypesAsync = async () => {
        const ctypes: CancerTypeIF[] = [];
        ctypes.push({ name: "膀胱癌", code: "Bladder" });
        ctypes.push({ name: "乳癌", code: "Breast" });
        ctypes.push({ name: "中樞神經腫瘤", code: "CentralNervous" });
        ctypes.push({ name: "子宮頸癌", code: "Cervical" });
        ctypes.push({ name: "大腸直腸癌", code: "ColonRectum" });
        ctypes.push({ name: "子宮體癌", code: "Endometrium" });
        ctypes.push({ name: "食道癌", code: "Esophageal" });
        ctypes.push({ name: "頭頸癌", code: "HeadNeck" });
        ctypes.push({ name: "肝癌", code: "Liver" });
        ctypes.push({ name: "肺癌", code: "Lung" });
        ctypes.push({ name: "淋巴癌", code: "Lymphoma" });
        ctypes.push({ name: "血液癌症", code: "Oncology" });
        ctypes.push({ name: "其他癌症", code: "Others" });
        ctypes.push({ name: "卵巢癌", code: "Ovary" });
        ctypes.push({ name: "胰臟癌", code: "Pancreas" });
        ctypes.push({ name: "攝護腺癌", code: "Prostate" });
        ctypes.push({ name: "胃癌", code: "Stomach" });
        ctypes.push({ name: "其他癌症", code: "Others" });
        setCancerTypes(ctypes);
    };
    // 進入頁面選項 
    const actions = [
        { name: '病人詳細資料', code: 'caseDetail' },
        { name: '團隊會議', code: 'teamMeeting' }
    ];
    const GetCancerMRList = useCallback(async (condition: CancerBasicCondition) => {
        const convertRowDataToCancerPersonBasicIF = (data: CancerBasicIF[]): CancerPersonBasicIF[] => {
            var cancerPersonBasicList: CancerPersonBasicIF[] = [];
            data.forEach((item) => {
                // 如果cancerPersonBasicIF裡面已經有這個人的資料，就把這筆資料加到這個人的cancerData裡面
                var cancerPerson = cancerPersonBasicList.find((person) => person.mrNo === item.mrNo);
                if (cancerPerson) {
                    if (cancerPerson.firstDate > item.initialICD9Date) {
                        cancerPerson.firstDate = item.initialICD9Date;
                    }
                    cancerPerson.cancerData.push({
                        cancerNo: item.cancerNo,
                        icd9Code: item.icd9Code,
                        initialICD9Date: item.initialICD9Date,
                        firstCat: item.firstCat,
                        cancerType: item.cancerType,
                        cancerTypeC: item.cancerTypeC,
                    });
                } else {
                    var newCancerPerson: CancerPersonBasicIF = {
                        mrNo: item.mrNo,
                        name: item.name,
                        birthday: item.birthday,
                        sex: item.sex,
                        add: item.add,
                        death: item.death,
                        tel: item.tel,
                        firstDate: item.initialICD9Date,
                        cancerData: [{
                            cancerNo: item.cancerNo,
                            icd9Code: item.icd9Code,
                            initialICD9Date: item.initialICD9Date,
                            firstCat: item.firstCat,
                            cancerType: item.cancerType,
                            cancerTypeC: item.cancerTypeC,
                        }]
                    };
                    cancerPersonBasicList.push(newCancerPerson);
                }
            });

            cancerPersonBasicList.forEach((item) => {
                item.firstDate = convertToTWDateHyphenFromDateString(item.firstDate);
                item.birthday = convertToTWDateHyphenFromDateString(item.birthday);
                item.cancerData.forEach((data) => {
                    // 將初診斷日轉民國年月日
                    data.initialICD9Date = convertToTWDateHyphenFromDateString(data.initialICD9Date);
                });
            });

            // 依照mrNo排序  由小到大  但是開頭是數字的排在最後面
            cancerPersonBasicList.sort((a, b) => {
                if (isNaN(Number(a.mrNo.charAt(0))) && isNaN(Number(b.mrNo.charAt(0)))) {
                    return a.mrNo.localeCompare(b.mrNo);
                } else if (isNaN(Number(a.mrNo.charAt(0))) && !isNaN(Number(b.mrNo.charAt(0)))) {
                    return -1;
                } else if (!isNaN(Number(a.mrNo.charAt(0))) && isNaN(Number(b.mrNo.charAt(0)))) {
                    return 1;
                } else {
                    return a.mrNo.localeCompare(b.mrNo);
                }
            });

            return cancerPersonBasicList;
        }

        try {
            var data: CancerBasicIF[] = await mrDataService.GetCancerMRList(condition);
            data.forEach((item) => {
                item.cancerTypeC = cancerTypes.find((cancerType) => cancerType.code === item.cancerType)?.name || '';
                item.sex = item.sex === '1' ? '男' : item.sex === '2' ? '女' : item.sex;
            });
            setCancerPatients(convertRowDataToCancerPersonBasicIF(data));
            setSearchData((prevSearchData) => ({
                ...prevSearchData,
                mrno: '',
                name: '',
                mrNoOrName: '',
            }));
        } catch (err: any) {
            if (err.response) {
                showInfo(err.response.data);
            }
            setCancerPatients([]);
            console.log(err);
        }
    }, [cancerTypes]);

    useEffect(() => {
        if (cancerTypes.length === 0) {
            getCancerTypesAsync();
        }
    }, [cancerTypes.length]);

    useEffect(() => {
        const autoSearch = async () => {
            if (mrNo || name || mrNoOrName) {
                setSearchData(prev => ({
                    ...prev,
                    mrno: mrNo?.toString() || '',
                    name: name || '',
                    mrNoOrName: mrNoOrName || ''
                }));

                const condition: CancerBasicCondition = {
                    mrNo: mrNo?.toString() || '',
                    name: name || '',
                    mrNoOrName: mrNoOrName || '',
                    cancerEName: []
                };

                try {
                    setCancerPatients(null);
                    await GetCancerMRList(condition);
                } catch (error) {
                    console.error('自動搜尋失敗:', error);
                    showError('搜尋失敗，請稍後再試');
                }
            }
        };

        autoSearch();
    }, [mrNo, name, mrNoOrName]);

    const handleSearch = () => {

        var condition: CancerBasicCondition = {
            mrNo: searchData.mrno,
            cancerEName: Array.isArray(searchData.selectedCancerType) ? searchData.selectedCancerType.map(item => item.code) : [],
            name: searchData.name,
            mrNoOrName: searchData.mrNoOrName,
        };
        setCancerPatients(null);
        GetCancerMRList(condition);

    }



    const handleReset = () => {
        setSearchData({
            mrno: '',
            name: '',
            mrNoOrName: '',
            selectedCancerType: [] as MultiSelectIF[],
        });
        setCancerPatients([]);
    }
    const handleCaseDetail = (mrNo: string, patientName: string) => {
        const tab: TabItemIF = {
            title: '個案：' + patientName,
            tabID: 'CaseDetail-' + mrNo,
            content: <CaseDetail key={Date.now()} mrNo={mrNo} patientName={patientName} />,
        };
        addTab(tab);
    }
    const handleCaseTeamMeeting = (mrNo: string, patientName: string) => {
        // addTab
        const tab: TabItemIF = {
            title: '個案：' + patientName + '-團隊會議',
            tabID: 'TeamMeeting-' + mrNo,
            content: <BlankFrame url={getTeamMeetingUrl(mrNo)} />,
        };

        addTab(tab);
    }
    const getTeamMeetingUrl = (mrNo: string) => {
        return 'http://hlcandbsvr/CancerPlan/TeamMeeting?MRNo=' + mrNo + '&CancerName=ALL&Edit=N&DocNo=&UserID=' + UserBasic.userID;
    }



    return (
        <>

            <Toast ref={(ref) => setToastRef(ref)} />
            <div className='card flex-items '>
                {/* 姓名 */}
                <InputText name='caseName' value={searchData.name} onChange={handleSearchDataChange} placeholder='姓名'
                    onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            handleSearch();
                        }
                    }}></InputText>
                {/* 身分證字號 */}
                <InputText name='mrno' value={searchData.mrno} onChange={handleSearchDataChange} placeholder='身分證字號'
                    onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            handleSearch();
                        }
                    }}></InputText>
                {/* 姓名或病歷號 */}

                <InputText name='mrNoOrName' value={searchData.mrNoOrName} onChange={handleSearchDataChange} placeholder='姓名或病歷號'
                    className='hidden'
                    onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            handleSearch();
                        }
                    }}></InputText>
                {/* 癌別 */}
                <MultiSelect name='selectedCancerType' value={searchData.selectedCancerType} onChange={(e) => setSearchData({ ...searchData, selectedCancerType: e.value })}
                    options={cancerTypes} optionLabel="name" filter maxSelectedLabels={3} placeholder="選擇癌別" className="md:w-20rem" showClear />
                {/* 查詢 */}
                <Button label="查詢" icon="pi pi-search" className="p-button-raised p-button-rounded p-button-text bg-blue-600 text-white"
                    onClick={() => {
                        setSearchData({ ...searchData, mrNoOrName: '' });
                        handleSearch();
                    }} />
                {/* 重置 */}
                <Button label="重置" icon="pi pi-refresh" className="p-button-raised p-button-rounded p-button-text "
                    onClick={() => handleReset()} />
                {/* test button */}
                {/* <Button label="test" icon="pi pi-refresh" className="p-button-raised p-button-rounded p-button-text "
                    onClick={() => console.log(searchData.selectedCancerType)} /> */}
            </div>
            <div className='card mt-1'>
                {cancerPatients && cancerPatients.length > 0 && <div className='text-right'>查詢時間: {new Date().toLocaleString()} {"\u00A0\u00A0\u00A0"} 共{cancerPatients.length}筆</div>}
                <DataTable value={cancerPatients || []} tableStyle={{ minWidth: '50rem' }} stripedRows
                    paginator rows={10} rowsPerPageOptions={[5, 10, 25, 50, 100]} paginatorPosition='both'
                    columnResizeMode="expand" resizableColumns showGridlines loading={cancerPatients == null}
                    removableSort className='mr-1'
                >

                    {/* 進入此病人 */}
                    {/* <Column header="進入此病人" headerClassName="text-center" className='max-w-22' body={(rowData: CancerPersonBasicIF) => (
                        <div>
                            <Button icon="pi pi-arrow-right" label='進入' className="p-button-raised p-button-rounded p-button-text bg-blue-600 text-white"
                                onClick={() => handleCaseDetail(rowData.mrNo, rowData.name)} />
                             <Button icon="pi pi-arrow-right" label='團隊會議' className="p-button-raised p-button-rounded p-button-text bg-blue-600 text-white"
                                onClick={() => handleCaseTeamMeeting(rowData.mrNo, rowData.name)} /> 
                        </div>
                    )} /> */}
                    <Column header="進入此病人" headerClassName="text-center" className='max-w-22' body={(rowData: CancerPersonBasicIF, options) => (
                        <div>
                            <Button label={`${options.rowIndex + 1}`} className="p-button-raised p-button-rounded p-button-text bg-blue-600 text-white"
                                onClick={() => handleCaseDetail(rowData.mrNo, rowData.name)} />
                        </div>
                    )} />
                    {/* Row 序號 # */}
                    {/* <Column headerClassName="text-center" className="font-semibold" header="#" body={(rowData, index) => index.rowIndex + 1} style={{ width: '3rem' }} /> */}
                    <Column field="name" header="姓名" className=' font-semibold text-xl'></Column>
                    <Column field="mrNo" header="病歷號" sortable className=' font-semibold'></Column>
                    <Column field="sex" header="性別" sortable className=' font-semibold'></Column>
                    <Column field="birthday" header="生日" sortable className=' font-semibold'></Column>
                    <Column field="firstDate" header="初診斷癌症日" sortable className=' font-semibold'></Column>
                    <Column field="cancerData" header="內容" headerClassName="text-center" className=' font-semibold'
                        body={(rowData: CancerPersonBasicIF) => (
                            <ul style={{ listStyleType: 'none', padding: 0 }}>
                                {rowData.cancerData.map((data, index) => (
                                    <li key={index} className='bg-gray-100'>
                                        <div style={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', border: '1px solid #ddd' }}>
                                            <span style={{ border: '1px solid #ddd', paddingLeft: '10px', flex: '1 0 150px', wordWrap: 'break-word' }}>診斷癌別:{data.cancerTypeC}</span>
                                            <span style={{ border: '1px solid #ddd', paddingRight: '10px', paddingLeft: '10px', flex: '1 0 150px', wordWrap: 'break-word' }}>初始分類:{data.firstCat}</span>
                                            <span style={{ border: '1px solid #ddd', paddingRight: '10px', flex: '1 0 150px', wordWrap: 'break-word' }}>診斷碼: {data.icd9Code}</span>
                                            <span style={{ border: '1px solid #ddd', paddingRight: '10px', paddingLeft: '10px', flex: '1 0 150px', wordWrap: 'break-word' }}>初診斷日:{data.initialICD9Date}</span>
                                        </div>
                                    </li>
                                ))}
                            </ul>
                        )} />
                </DataTable>
            </div>


        </>
    );
};

/*
            <div className='card mt-1'>
                <DataTable value={cancerBasics} tableStyle={{ minWidth: '50rem' }} stripedRows
                    paginator rows={10} rowsPerPageOptions={[5, 10, 25, 50, 100]} columnResizeMode="expand" resizableColumns showGridlines
                    className='h-[100vh] overflow-auto'
                    rowGroupMode="rowspan" groupRowsBy="mrNo" sortMode="multiple" sortField="mrNo" sortOrder={1}
                >
                    <Column headerClassName="text-center" className="font-semibold" header="#" body={(rowData, index) => index.rowIndex + 1} style={{ width: '3rem' }} />
                    <Column field="name" header="姓名" className=' font-semibold text-xl'></Column>
                    <Column field="mrNo" header="病歷號" className=' font-semibold'></Column>
                    <Column field="birthday" header="生日" className=' font-semibold'></Column>
                    <Column field="cancerTypeC" header="診斷癌別" className=' font-semibold'></Column>
                    <Column field="initialICD9Date" header="初診斷日" className=' font-semibold'></Column>
                    <Column field="icd9Code" header="初診斷碼" className=' font-semibold'></Column>
                    <Column headerClassName="text-center" className="" body={(rowData: CancerPersonBasicIF) => (
                        <Button label="進入此病人" icon="pi pi-arrow-right" className="p-button-raised p-button-rounded p-button-text bg-blue-600 text-white"
                            onClick={() => console.log(rowData)} />
                    )} />
                </DataTable>
            </div>
*/

export default CaseSearch;

