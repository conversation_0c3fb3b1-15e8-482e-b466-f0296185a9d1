'use client';
import React from 'react';
import { useIsMobile, getDeviceType, useScreenOrientation } from '@/hooks/useIsMobile';
import { Card } from 'primereact/card';
import { Badge } from 'primereact/badge';

/**
 * 裝置資訊顯示組件
 * 用於測試和展示行動裝置檢測功能
 */
const DeviceInfo: React.FC = () => {
    const isMobile = useIsMobile();
    const orientation = useScreenOrientation();
    
    // 獲取裝置資訊
    const getDeviceInfo = () => {
        if (typeof window === 'undefined') return {};
        
        return {
            userAgent: navigator.userAgent,
            deviceType: getDeviceType(),
            screenWidth: window.innerWidth,
            screenHeight: window.innerHeight,
            pixelRatio: window.devicePixelRatio,
            touchSupport: 'ontouchstart' in window,
            maxTouchPoints: navigator.maxTouchPoints,
            platform: navigator.platform,
            language: navigator.language,
        };
    };

    const deviceInfo = getDeviceInfo();

    return (
        <div className="p-4 space-y-4">
            <h2 className="text-2xl font-bold mb-4">裝置檢測資訊</h2>
            
            {/* 主要檢測結果 */}
            <Card title="檢測結果" className="mb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between">
                        <span className="font-medium">是否為行動裝置：</span>
                        <Badge 
                            value={isMobile ? "是" : "否"} 
                            severity={isMobile ? "success" : "info"}
                        />
                    </div>
                    <div className="flex items-center justify-between">
                        <span className="font-medium">螢幕方向：</span>
                        <Badge 
                            value={orientation === 'portrait' ? "直向" : "橫向"} 
                            severity="secondary"
                        />
                    </div>
                    <div className="flex items-center justify-between">
                        <span className="font-medium">裝置類型：</span>
                        <Badge 
                            value={deviceInfo.deviceType} 
                            severity="warning"
                        />
                    </div>
                    <div className="flex items-center justify-between">
                        <span className="font-medium">觸控支援：</span>
                        <Badge 
                            value={deviceInfo.touchSupport ? "支援" : "不支援"} 
                            severity={deviceInfo.touchSupport ? "success" : "danger"}
                        />
                    </div>
                </div>
            </Card>

            {/* 詳細資訊 */}
            <Card title="詳細資訊" className="mb-4">
                <div className="space-y-2">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                        <div><strong>螢幕寬度：</strong> {deviceInfo.screenWidth}px</div>
                        <div><strong>螢幕高度：</strong> {deviceInfo.screenHeight}px</div>
                        <div><strong>像素比：</strong> {deviceInfo.pixelRatio}</div>
                        <div><strong>最大觸控點：</strong> {deviceInfo.maxTouchPoints}</div>
                        <div><strong>平台：</strong> {deviceInfo.platform}</div>
                        <div><strong>語言：</strong> {deviceInfo.language}</div>
                    </div>
                </div>
            </Card>

            {/* User Agent */}
            <Card title="User Agent" className="mb-4">
                <div className="text-sm break-all bg-gray-100 p-3 rounded">
                    {deviceInfo.userAgent}
                </div>
            </Card>

            {/* 測試說明 */}
            <Card title="測試說明" className="mb-4">
                <div className="text-sm space-y-2">
                    <p>• <strong>行動裝置檢測：</strong> 基於 User Agent、螢幕寬度（≤768px）和觸控支援</p>
                    <p>• <strong>響應式測試：</strong> 調整瀏覽器視窗大小來測試響應式行為</p>
                    <p>• <strong>觸控測試：</strong> 在觸控裝置上測試觸控功能</p>
                    <p>• <strong>方向測試：</strong> 在行動裝置上旋轉螢幕測試方向檢測</p>
                </div>
            </Card>

            {/* 側邊欄行為說明 */}
            <Card title="側邊欄行為" className="mb-4">
                <div className="text-sm space-y-2">
                    <p><strong>桌面版（isMobile = false）：</strong></p>
                    <ul className="list-disc list-inside ml-4 space-y-1">
                        <li>側邊欄固定在左側</li>
                        <li>可以展開/收合</li>
                        <li>深色主題</li>
                        <li>顯示切換按鈕</li>
                    </ul>
                    
                    <p><strong>行動版（isMobile = true）：</strong></p>
                    <ul className="list-disc list-inside ml-4 space-y-1">
                        <li>側邊欄變成彈出式 Sidebar</li>
                        <li>淺色主題</li>
                        <li>點擊項目後自動關閉</li>
                        <li>顯示漢堡選單按鈕</li>
                    </ul>
                </div>
            </Card>
        </div>
    );
};

export default DeviceInfo;
